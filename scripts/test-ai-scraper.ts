#!/usr/bin/env tsx

import {
    createAiGenericScraper,
    defaultAiScraperConfig
} from '../src/server/scraper/implementations/listings/aiGeneric'
import { testOllamaConnection } from '../src/server/scraper/utils/aiScraper'

/**
 * Simple CLI script to test the AI scraper functionality
 */
async function main() {
    const args = process.argv.slice(2)

    if (args.length === 0) {
        console.log(`
Usage: tsx scripts/test-ai-scraper.ts <command> [options]

Commands:
  test-connection [endpoint] [model]  - Test connection to Ollama server
  scrape-url <url> [endpoint] [model] - Scrape a single URL

Examples:
  tsx scripts/test-ai-scraper.ts test-connection
  tsx scripts/test-ai-scraper.ts test-connection http://localhost:11434 llama3.2
  tsx scripts/test-ai-scraper.ts scrape-url https://www.3djake.de/filament/pet-filament
  tsx scripts/test-ai-scraper.ts scrape-url https://example.com http://localhost:11434 mistral
        `)
        process.exit(1)
    }

    const command = args[0]

    try {
        switch (command) {
            case 'test-connection':
                await testConnection(args[1], args[2])
                break
            case 'scrape-url':
                if (!args[1]) {
                    console.error(
                        'Error: URL is required for scrape-url command'
                    )
                    process.exit(1)
                }
                await scrapeUrl(args[1], args[2], args[3])
                break
            default:
                console.error(`Error: Unknown command '${command}'`)
                process.exit(1)
        }
    } catch (error) {
        console.error('Error:', error instanceof Error ? error.message : error)
        process.exit(1)
    }
}

/**
 * Test connection to Ollama server
 */
async function testConnection(endpoint?: string, model?: string) {
    const config = {
        ...defaultAiScraperConfig,
        endpoint: endpoint || defaultAiScraperConfig.endpoint,
        model: model || defaultAiScraperConfig.model
    }

    console.log(`Testing connection to Ollama server...`)
    console.log(`Endpoint: ${config.endpoint}`)
    console.log(`Model: ${config.model}`)

    const isConnected = await testOllamaConnection(config)

    if (isConnected) {
        console.log('✅ Connection successful!')
    } else {
        console.log('❌ Connection failed!')
        console.log('\nTroubleshooting:')
        console.log('1. Make sure Ollama is running: ollama serve')
        console.log('2. Check if the model is available: ollama list')
        console.log(`3. Pull the model if needed: ollama pull ${config.model}`)
        console.log('4. Verify the endpoint URL is correct')
    }
}

/**
 * Scrape a single URL using AI
 */
async function scrapeUrl(url: string, endpoint?: string, model?: string) {
    const config = {
        ...defaultAiScraperConfig,
        endpoint: endpoint || defaultAiScraperConfig.endpoint,
        model: model || defaultAiScraperConfig.model
    }

    console.log(`Scraping URL with AI...`)
    console.log(`URL: ${url}`)
    console.log(`Endpoint: ${config.endpoint}`)
    console.log(`Model: ${config.model}`)
    console.log('')

    // Test connection first
    console.log('Testing Ollama connection...')
    const isConnected = await testOllamaConnection(config)

    if (!isConnected) {
        console.error(
            '❌ Cannot connect to Ollama server. Please check your setup.'
        )
        return
    }

    console.log('✅ Ollama connection successful')
    console.log('')

    // Create scraper and scrape the URL
    const scraper = createAiGenericScraper(config)

    console.log('Starting scraping process...')
    const startTime = Date.now()

    try {
        const products = await scraper.scrapeUrl(url, {
            headless: true,
            timeout: 30000,
            retries: 3,
            delayMin: 500,
            delayMax: 3000
        })

        const endTime = Date.now()
        const duration = ((endTime - startTime) / 1000).toFixed(2)

        console.log(`\n✅ Scraping completed in ${duration}s`)
        console.log(`Found ${products.length} products:`)
        console.log('')

        if (products.length === 0) {
            console.log('No products found. This could mean:')
            console.log('1. The page has no 3D printing filament products')
            console.log('2. The AI model needs better instructions')
            console.log(
                '3. The page structure is complex and needs preprocessing'
            )
        } else {
            products.forEach((product, index) => {
                console.log(`${index + 1}. ${product.name}`)
                console.log(`   Brand: ${product.brand}`)
                console.log(`   Material: ${product.materialType}`)
                console.log(`   Color: ${product.color}`)
                console.log(`   Weight: ${product.weight}g`)
                console.log(
                    `   Price: ${product.currentPrice / 100} ${product.currency}`
                )
                console.log(`   Identifier: ${product.identifier}`)
                console.log('')
            })
        }
    } catch (error) {
        console.error(
            '❌ Scraping failed:',
            error instanceof Error ? error.message : error
        )
    }
}

// Run the script
main().catch(console.error)
