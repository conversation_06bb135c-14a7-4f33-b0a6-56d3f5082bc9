import { PrismaClient } from '@prisma/client'
import { createScrapingService } from '~/server/scraper/productService'

async function main() {
    const prisma = new PrismaClient()

    try {
        console.log('Starting bulk scrape of all products...')

        const scrapingService = createScrapingService(prisma)
        const results = await scrapingService.scrapeAllProducts({})

        console.log(`Successfully scraped ${results.length} products`)
    } catch (error) {
        console.error('Failed to scrape products:', error)
        process.exit(1)
    } finally {
        await prisma.$disconnect()
    }
}

main()
