{"name": "filafind", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "db:generate": "prisma generate", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "dev": "next dev --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "scrape": "tsx scripts/scrape-all.ts", "ai-scraper": "tsx scripts/test-ai-scraper.ts", "start": "next start", "typecheck": "tsc --noEmit", "chakra:theme": "pnpx @chakra-ui/cli typegen ./src/app/theme.ts"}, "dependencies": {"@chakra-ui/react": "^3.16.0", "@emotion/react": "^11.14.0", "@prisma/client": "^6.5.0", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.69.0", "@trpc/client": "^11.0.0", "@trpc/next": "^11.1.2", "@trpc/react-query": "^11.0.0", "@trpc/server": "^11.0.0", "next": "^15.2.3", "next-themes": "^0.4.6", "ollama": "^0.5.15", "playwright": "^1.52.0", "playwright-extra": "^4.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "server-only": "^0.0.1", "superjson": "^2.2.1", "tsx": "^4.19.3", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/node": "^20.14.10", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "eslint": "^9.23.0", "eslint-config-next": "^15.2.3", "prettier": "^3.5.3", "prisma": "^6.5.0", "typescript": "^5.8.2", "typescript-eslint": "^8.27.0"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "packageManager": "pnpm@9.15.9"}