// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
    provider = "prisma-client-js"
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

model Product {
    id               String       @id @default(cuid())
    name             String
    identifier       String
    store            Store
    brand            String
    currentPrice     Int
    currency         Currency     @default(EUR)
    materialType     MaterialType
    weight           Int // Weight in grams
    pricePerKg       Float // Price per kilogram for easier sorting and filtering
    color            String
    lastChecked      DateTime
    createdAt        DateTime     @default(now())
    updatedAt        DateTime     @updatedAt
    historicalPrices Price[]
    locations        Location[]   @default([Germany])
    scrapeJobs       ScrapeJob[]

    @@unique(name: "storeIdentifier", [store, identifier])
    @@index([pricePerKg]) // Add index for faster sorting by price per kg
}

model Price {
    id        String   @id @default(cuid())
    value     Int
    timestamp DateTime
    product   Product  @relation(fields: [productId], references: [id])
    productId String
}

enum Currency {
    EUR
    USD
    GBP
    CAD
}

enum MaterialType {
    PLA
    PETG
    ABS
    TPU
    ASA
    NYLON
    OTHER
}

enum Store {
    amazon
    prusa3d
    threeDJake
}

enum Location {
    Germany
    UnitedStates
    UnitedKingdom
    Canada
}

enum JobStatus {
    pending
    processing
    completed
    failed
}

model ScrapeJob {
    id              String    @id @default(cuid())
    product         Product   @relation(fields: [productId], references: [id])
    productId       String
    priority        Int       @default(1)
    status          JobStatus @default(pending)
    scheduledFor    DateTime  @default(now())
    lastAttemptedAt DateTime?
    nextAttemptAt   DateTime?
    attempts        Int       @default(0)
    maxAttempts     Int       @default(3)
    createdAt       DateTime  @default(now())
    completedAt     DateTime?
    error           String?
    lockedBy        String?
    lockedAt        DateTime?

    @@index([status, scheduledFor])
    @@index([productId])
    @@index([status, priority, createdAt])
}

model ListingScrapeJob {
    id              String    @id @default(cuid())
    store           Store
    priority        Int       @default(1)
    status          JobStatus @default(pending)
    scheduledFor    DateTime  @default(now())
    lastAttemptedAt DateTime?
    nextAttemptAt   DateTime?
    attempts        Int       @default(0)
    maxAttempts     Int       @default(3)
    createdAt       DateTime  @default(now())
    completedAt     DateTime?
    errorMessage    String?
    lockedBy        String?
    lockedAt        DateTime?

    @@index([status, scheduledFor])
    @@index([store])
    @@index([status, priority, createdAt])
}
