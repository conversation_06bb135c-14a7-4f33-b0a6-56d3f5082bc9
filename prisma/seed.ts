import {
    PrismaClient,
    MaterialType,
    Store,
    Currency,
    Location
} from '@prisma/client'
import { createProductService } from '../src/server/product/service'
import { type CreateProduct } from '../src/server/product/schemas'
import * as fs from 'fs'
import * as path from 'path'
import { fileURLToPath } from 'url'

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const prisma = new PrismaClient()
const productService = createProductService(prisma)

// Interface for JSON product data
interface ProductJson {
    name: string
    identifier: string
    store: string
    brand: string
    currentPrice: number
    currency: string
    materialType: string
    weight: number
    color: string
    locations: string[]
}

// Function to convert JSON data to CreateProduct type
function convertJsonToCreateProduct(jsonProduct: ProductJson): CreateProduct {
    return {
        name: jsonProduct.name,
        identifier: jsonProduct.identifier,
        store: Store[jsonProduct.store as keyof typeof Store],
        brand: jsonProduct.brand,
        currentPrice: jsonProduct.currentPrice,
        currency: Currency[jsonProduct.currency as keyof typeof Currency],
        materialType:
            MaterialType[jsonProduct.materialType as keyof typeof MaterialType],
        weight: jsonProduct.weight,
        color: jsonProduct.color,
        locations: jsonProduct.locations.map(
            (loc) => Location[loc as keyof typeof Location]
        )
    }
}

async function main() {
    console.log('Starting seeding...')

    try {
        // Get all JSON files from the seed-data directory
        const seedDataDir = path.join(__dirname, 'seed-data')
        const jsonFiles = fs
            .readdirSync(seedDataDir)
            .filter((file) => file.endsWith('.json'))

        console.log(`Found ${jsonFiles.length} brand JSON files`)

        // Read and parse all JSON files
        let allProducts: ProductJson[] = []

        for (const jsonFile of jsonFiles) {
            const filePath = path.join(seedDataDir, jsonFile)
            const fileContent = fs.readFileSync(filePath, 'utf8')
            const products = JSON.parse(fileContent) as ProductJson[]

            console.log(`Loaded ${products.length} products from ${jsonFile}`)
            allProducts = [...allProducts, ...products]
        }

        // Convert JSON data to CreateProduct type
        const products: CreateProduct[] = allProducts.map(
            convertJsonToCreateProduct
        )

        console.log(
            `Loaded ${products.length} products total from all JSON files`
        )

        // Use upsert to avoid duplicates when re-running the seed
        for (const productData of products) {
            try {
                const product = await productService.upsert(productData)
                console.log(`Created/updated product: ${product.name}`)
            } catch (error) {
                console.error(
                    `Error creating product ${productData.name}:`,
                    error
                )
            }
        }
    } catch (error) {
        console.error('Error processing products data:', error)
    }

    console.log('Seeding completed successfully!')
}

main()
    .catch((e) => {
        console.error('Error during seeding:', e)
        process.exit(1)
    })
    .finally(async () => {
        await prisma.$disconnect()
    })
