[{"name": "Prusament PETG Jet Black 1kg", "identifier": "prusament-petg-jet-black", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "<PERSON>", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Prusa Orange 1kg", "identifier": "prusament-petg-prusa-orange", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "Prusa Orange", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Urban Grey 1kg", "identifier": "prusament-petg-urban-grey", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "<PERSON>", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Neon Green Transparent 1kg", "identifier": "prusament-petg-neon-green-transparent", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "Neon Green Transparent", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Carmine Red Transparent 1kg", "identifier": "prusament-petg-carmine-red-transparent", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "Carmine Red Transparent", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Ultramarine Blue Transparent 1kg", "identifier": "prusament-petg-ultramarine-blue-transparent", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "Ultramarine Blue Transparent", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Yellow Gold 1kg", "identifier": "prusament-petg-yellow-gold", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "Yellow Gold", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Galaxy Black 1kg", "identifier": "prusament-petg-galaxy-black", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "Galaxy Black", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Clear 1kg", "identifier": "prusament-petg-clear", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "Clear", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Signal White 1kg", "identifier": "prusament-petg-signal-white", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "<PERSON> White", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Pistachio Green 1kg", "identifier": "prusament-petg-pistachio-green", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "Pistachio Green", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Terracotta Light 1kg", "identifier": "prusament-petg-terracotta-light", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "Terracotta Light", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Chalky Blue 1kg", "identifier": "prusament-petg-chalky-blue", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "Chalky Blue", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Mango Yellow 1kg", "identifier": "prusament-petg-mango-yellow", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "Mango Yellow", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Anthracite Grey 1kg", "identifier": "prusament-petg-anthracite-grey", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "Anthracite Grey", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Ocean Blue 1kg", "identifier": "prusament-petg-ocean-blue", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "Ocean Blue", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Jungle Green 1kg", "identifier": "prusament-petg-jungle-green", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "Jungle Green", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Lipstick Red 1kg", "identifier": "prusament-petg-lipstick-red", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "Lipstick Red", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Orange Transparent 1kg", "identifier": "prusament-petg-orange-transparent", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "Orange Transparent", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Matte Black 1kg", "identifier": "prusament-petg-matte-black", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "<PERSON><PERSON>", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Prusa Orange 2kg", "identifier": "prusament-petg-prusa-orange-2kg", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 2000, "color": "Prusa Orange", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Jet Black 2kg", "identifier": "prusament-petg-jet-black-2kg", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 2000, "color": "<PERSON>", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Urban Grey 2kg", "identifier": "prusament-petg-urban-grey-2kg", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 2000, "color": "<PERSON>", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Signal White 2kg", "identifier": "prusament-petg-signal-white-2kg", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 2000, "color": "<PERSON> White", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Clear 2kg", "identifier": "prusament-petg-clear-2kg", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 2000, "color": "Clear", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Anthracite Grey 2kg", "identifier": "prusament-petg-anthracite-grey-2kg", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 2000, "color": "Anthracite Grey", "locations": ["Germany", "UnitedKingdom"]}, {"name": "Prusament PETG Prusa Pro Green 1kg", "identifier": "prusament-petg-prusa-pro-green", "store": "prusa3d", "brand": "Prusament", "currentPrice": 0, "currency": "EUR", "materialType": "PETG", "weight": 1000, "color": "Prusa Pro Green", "locations": ["Germany", "UnitedKingdom"]}]