import { Store } from '@prisma/client'

/**
 * Generate a product URL based on the store and identifier
 * @param store The store enum value
 * @param identifier The product identifier
 * @returns The complete product URL
 */
export const generateProductUrl = (
    store: Store,
    identifier: string
): string => {
    switch (store) {
        case Store.amazon:
            return `https://www.amazon.de/dp/${identifier}`
        case Store.prusa3d:
            return `https://www.prusa3d.com/de/produkt/${identifier}`
        case Store.threeDJake:
            return `https://www.3djake.de/${identifier}`
        default:
            // Return a fallback URL or throw an error for unknown stores
            throw new Error(`Unknown store: ${store}`)
    }
}
