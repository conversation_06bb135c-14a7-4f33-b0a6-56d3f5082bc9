import {
    type PrismaClient,
    type Prisma,
    type Store,
    type Location,
    type MaterialType
} from '@prisma/client'
import { type CreateProduct } from './schemas'
import { generateProductUrl } from './url'

export const createProductService = (db: PrismaClient) => {
    const getLatest = async () => {
        const product = await db.product.findFirst({
            orderBy: { createdAt: 'desc' }
        })

        if (!product) {
            throw new Error('No products found')
        }

        // Add productUrl to the returned product
        return {
            ...product,
            productUrl: generateProductUrl(product.store, product.identifier)
        }
    }

    const create = async (data: CreateProduct) => {
        // Calculate price per kg directly
        const pricePerKg = data.currentPrice / (data.weight / 1000)

        return db.product.create({
            data: {
                ...data,
                pricePerKg,
                lastChecked: new Date(),
                historicalPrices: {
                    create: {
                        value: data.currentPrice,
                        timestamp: new Date()
                    }
                }
            }
        })
    }

    const upsert = async (data: CreateProduct) => {
        // Calculate price per kg directly
        const pricePerKg = data.currentPrice / (data.weight / 1000)

        return db.product.upsert({
            where: {
                storeIdentifier: {
                    store: data.store,
                    identifier: data.identifier
                }
            },
            update: {
                name: data.name,
                brand: data.brand,
                currentPrice: data.currentPrice,
                currency: data.currency,
                materialType: data.materialType,
                weight: data.weight,
                pricePerKg,
                color: data.color,
                lastChecked: new Date(),
                historicalPrices: {
                    create: {
                        value: data.currentPrice,
                        timestamp: new Date()
                    }
                }
            },
            create: {
                ...data,
                pricePerKg,
                lastChecked: new Date(),
                historicalPrices: {
                    create: {
                        value: data.currentPrice,
                        timestamp: new Date()
                    }
                }
            }
        })
    }

    const getAllWithFilters = async ({
        brands,
        stores,
        location,
        materialTypes
    }: {
        brands?: string[]
        stores?: Store[]
        location?: Location
        materialTypes?: MaterialType[]
    }) => {
        const where: Prisma.ProductWhereInput = {}

        // If brands are specified, filter by those brands
        // If brands array is empty, no products should match (empty result)
        if (brands) {
            if (brands.length > 0) {
                where.brand = { in: brands }
            } else {
                // Return no products if brands array is empty
                return []
            }
        }

        // If stores are specified, filter by those stores
        // If stores array is empty, no products should match (empty result)
        if (stores) {
            if (stores.length > 0) {
                where.store = { in: stores }
            } else {
                // Return no products if stores array is empty
                return []
            }
        }

        // If a location is specified, filter by that location
        if (location) {
            where.locations = { has: location }
        }

        // If material types are specified, filter by those material types
        // If material types array is empty, no products should match (empty result)
        if (materialTypes) {
            if (materialTypes.length > 0) {
                where.materialType = { in: materialTypes }
            } else {
                // Return no products if material types array is empty
                return []
            }
        }

        // Price range filter removed

        // Get all products matching the filters and sort by pricePerKg
        const products = await db.product.findMany({
            where,
            orderBy: { pricePerKg: 'asc' } // Sort by price per kg (ascending)
            // No need to include historical prices as they're not needed by the client
        })

        // Add productUrl to each product
        return products.map((product) => ({
            ...product,
            productUrl: generateProductUrl(product.store, product.identifier)
        }))
    }

    return {
        getLatest,
        create,
        upsert,
        getAllWithFilters
    }
}

export type ProductService = ReturnType<typeof createProductService>
