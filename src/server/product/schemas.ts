import { z } from 'zod'
import { Currency, MaterialType, Store, Location } from '@prisma/client'

export const createProductSchema = z.object({
    name: z.string().min(1),
    identifier: z.string(),
    store: z.nativeEnum(Store),
    brand: z.string().min(1),
    currentPrice: z.number().int().positive(),
    currency: z.nativeEnum(Currency).default('EUR'),
    materialType: z.nativeEnum(MaterialType),
    weight: z.number().int().positive(), // Weight in grams
    color: z.string().min(1),
    locations: z
        .array(z.nativeEnum(Location))
        .min(1)
        .default([Location.Germany])
})

export type CreateProduct = z.infer<typeof createProductSchema>
