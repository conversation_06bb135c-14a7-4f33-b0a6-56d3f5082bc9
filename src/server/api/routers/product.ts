import { createTRPCRouter, publicProcedure } from '~/server/api/trpc'
import { createProductService } from '~/server/product/service'
import { z } from 'zod'
import { Store, Location, MaterialType } from '@prisma/client'

export const productRouter = createTRPCRouter({
    getLatest: publicProcedure.query(async ({ ctx }) => {
        const productService = createProductService(ctx.db)
        return await productService.getLatest()
    }),

    getFiltered: publicProcedure
        .input(
            z.object({
                brands: z.array(z.string()).optional(),
                stores: z.array(z.nativeEnum(Store)).optional(),
                location: z.nativeEnum(Location).optional(),
                materialTypes: z.array(z.nativeEnum(MaterialType)).optional()
            })
        )
        .query(async ({ ctx, input }) => {
            const productService = createProductService(ctx.db)
            return await productService.getAllWithFilters(input)
        }),

    getAllBrands: publicProcedure.query(async ({ ctx }) => {
        const brands = await ctx.db.product.findMany({
            select: { brand: true },
            distinct: ['brand'],
            orderBy: { brand: 'asc' }
        })
        return brands.map((b) => b.brand)
    }),

    getAllStores: publicProcedure.query(async ({ ctx }) => {
        const stores = await ctx.db.product.findMany({
            select: { store: true },
            distinct: ['store']
        })
        return stores.map((s) => s.store)
    }),

    getAllLocations: publicProcedure.query(async ({ ctx }) => {
        const products = await ctx.db.product.findMany({
            select: { locations: true }
        })

        // Collect all unique locations
        const uniqueLocations = new Set<Location>()
        products.forEach((product) => {
            product.locations.forEach((location) => {
                uniqueLocations.add(location)
            })
        })

        return Array.from(uniqueLocations)
    }),

    getAllMaterialTypes: publicProcedure.query(async ({ ctx }) => {
        const materialTypes = await ctx.db.product.findMany({
            select: { materialType: true },
            distinct: ['materialType']
        })
        return materialTypes.map((m) => m.materialType)
    })
})
