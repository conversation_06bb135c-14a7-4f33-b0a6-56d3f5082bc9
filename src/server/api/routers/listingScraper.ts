import { createTRPCRouter, adminProcedure } from '../trpc'
import { createListingScraperService } from '../../scraper/listingService'
import { createListingScraperQueue } from '../../scraper/listingQueue'
import { createAiScraperService } from '../../scraper/aiScraperService'
import {
    scraperOptionsSchema,
    aiScraperConfigSchema
} from '../../scraper/schemas'
import { z } from 'zod'
import { Store } from '@prisma/client'

export const listingScraperRouter = createTRPCRouter({
    /**
     * Scrape listings for all stores
     */
    scrapeAllListings: adminProcedure
        .input(
            z
                .object({
                    options: scraperOptionsSchema.optional()
                })
                .optional()
        )
        .mutation(async ({ ctx, input }) => {
            const listingScraperService = createListingScraperService(ctx.db)
            return await listingScraperService.scrapeAllListings(input?.options)
        }),

    /**
     * Scrape listings for a specific store
     */
    scrapeStoreListings: adminProcedure
        .input(
            z.object({
                store: z.nativeEnum(Store),
                options: scraperOptionsSchema.optional()
            })
        )
        .mutation(async ({ ctx, input }) => {
            const listingScraperService = createListingScraperService(ctx.db)
            return await listingScraperService.scrapeStoreListings(
                input.store,
                input.options
            )
        }),

    /**
     * Queue listing scraper jobs for all stores
     */
    queueAllStores: adminProcedure
        .input(
            z
                .object({
                    priority: z.number().min(1).max(10).default(1)
                })
                .optional()
        )
        .mutation(async ({ ctx, input }) => {
            const queue = createListingScraperQueue(ctx.db)
            return await queue.queueAllStores(input?.priority)
        }),

    /**
     * Queue a listing scraper job for a specific store
     */
    queueStore: adminProcedure
        .input(
            z.object({
                store: z.nativeEnum(Store),
                priority: z.number().min(1).max(10).default(1)
            })
        )
        .mutation(async ({ ctx, input }) => {
            const queue = createListingScraperQueue(ctx.db)
            return await queue.queueStore(input.store, input.priority)
        }),

    /**
     * Process the next listing scraper job in the queue
     */
    processNextJob: adminProcedure
        .input(
            z
                .object({
                    workerId: z.string().optional(),
                    options: scraperOptionsSchema.optional()
                })
                .optional()
        )
        .mutation(async ({ ctx, input }) => {
            const queue = createListingScraperQueue(ctx.db)
            return await queue.processNextJob(
                input?.workerId || `manual-${Date.now()}`,
                input?.options
            )
        }),

    /**
     * Reset stalled listing scraper jobs
     */
    resetStalledJobs: adminProcedure
        .input(
            z
                .object({
                    stalledMinutes: z.number().min(5).max(120).default(30)
                })
                .optional()
        )
        .mutation(async ({ ctx, input }) => {
            const queue = createListingScraperQueue(ctx.db)
            return await queue.resetStalledJobs(input?.stalledMinutes)
        }),

    /**
     * Get listing scraper queue statistics
     */
    getQueueStats: adminProcedure.query(async ({ ctx }) => {
        const queue = createListingScraperQueue(ctx.db)
        return await queue.getQueueStats()
    }),

    // AI Scraper endpoints

    /**
     * Test connection to Ollama server
     */
    testAiConnection: adminProcedure
        .input(aiScraperConfigSchema)
        .mutation(async ({ ctx, input }) => {
            const aiScraperService = createAiScraperService(ctx.db)
            return await aiScraperService.testConnection(input)
        }),

    /**
     * Scrape a single URL using AI
     */
    aiScrapeUrl: adminProcedure
        .input(
            z.object({
                url: z.string().url(),
                config: aiScraperConfigSchema.optional(),
                options: scraperOptionsSchema.optional()
            })
        )
        .mutation(async ({ ctx, input }) => {
            const aiScraperService = createAiScraperService(ctx.db)
            // Use default config if none provided
            const config = input.config || {}
            return await aiScraperService.scrapeUrl(
                input.url,
                config,
                input.options
            )
        }),

    /**
     * Scrape a URL using AI and save products to database
     */
    aiScrapeAndSave: adminProcedure
        .input(
            z.object({
                url: z.string().url(),
                config: aiScraperConfigSchema.optional(),
                options: scraperOptionsSchema.optional()
            })
        )
        .mutation(async ({ ctx, input }) => {
            const aiScraperService = createAiScraperService(ctx.db)
            // Use default config if none provided
            const config = input.config || {}
            return await aiScraperService.scrapeAndSave(
                input.url,
                config,
                input.options
            )
        }),

    /**
     * Scrape multiple URLs using AI with the same configuration
     */
    aiScrapeMultipleUrls: adminProcedure
        .input(
            z.object({
                urls: z.array(z.string().url()).min(1),
                config: aiScraperConfigSchema.optional(),
                options: scraperOptionsSchema.optional()
            })
        )
        .mutation(async ({ ctx, input }) => {
            const aiScraperService = createAiScraperService(ctx.db)
            // Use default config if none provided
            const config = input.config || {}
            return await aiScraperService.scrapeMultipleUrlsAndSave(
                input.urls,
                config,
                input.options
            )
        }),

    /**
     * Batch scrape with different configurations for different URLs
     */
    aiBatchScrapeWithConfigs: adminProcedure
        .input(
            z.object({
                urlConfigs: z
                    .array(
                        z.object({
                            url: z.string().url(),
                            config: aiScraperConfigSchema
                        })
                    )
                    .min(1),
                options: scraperOptionsSchema.optional()
            })
        )
        .mutation(async ({ ctx, input }) => {
            const aiScraperService = createAiScraperService(ctx.db)
            return await aiScraperService.batchScrapeWithDifferentConfigs(
                input.urlConfigs,
                input.options
            )
        })
})
