import { createTRPCRouter, publicProcedure, adminProcedure } from '../trpc'
import { createProductScrapingService } from '~/server/scraper/productService'
import { createProductScraperQueue } from '~/server/scraper/productQueue'
import { createScraperWorker } from '~/server/scraper/worker'
import {
    scrapeAndCreateParamsSchema,
    scrapeByIdSchema,
    scraperOptionsSchema
} from '~/server/scraper/schemas'
import { z } from 'zod'

// Create a single worker instance
let worker: ReturnType<typeof createScraperWorker> | null = null

export const scraperRouter = createTRPCRouter({
    // Original endpoints
    scrapeAll: publicProcedure
        .input(
            z.object({
                options: scraperOptionsSchema.optional()
            })
        )
        .mutation(async ({ ctx, input }) => {
            const scrapingService = createProductScrapingService(ctx.db)
            return await scrapingService.scrapeAllProducts(input)
        }),

    scrapeAndCreate: publicProcedure
        .input(scrapeAndCreateParamsSchema)
        .mutation(async ({ ctx, input }) => {
            const scrapingService = createProductScrapingService(ctx.db)
            return await scrapingService.scrapeAndCreate(input)
        }),

    scrapeById: publicProcedure
        .input(scrapeByIdSchema)
        .mutation(async ({ ctx, input }) => {
            const scrapingService = createProductScrapingService(ctx.db)
            return await scrapingService.scrapeById(input)
        }),

    // New queue-based endpoints
    queueAllProducts: adminProcedure
        .input(
            z
                .object({
                    priority: z.number().min(1).max(10).default(1)
                })
                .optional()
        )
        .mutation(async ({ ctx, input }) => {
            const queue = createProductScraperQueue(ctx.db)
            return await queue.queueAllProducts(input?.priority)
        }),

    queueProduct: adminProcedure
        .input(
            z.object({
                productId: z.string(),
                priority: z.number().min(1).max(10).default(1)
            })
        )
        .mutation(async ({ ctx, input }) => {
            const queue = createProductScraperQueue(ctx.db)
            return await queue.queueProduct(input.productId, input.priority)
        }),

    processNextJob: adminProcedure
        .input(
            z
                .object({
                    workerId: z.string().optional(),
                    options: scraperOptionsSchema.optional()
                })
                .optional()
        )
        .mutation(async ({ ctx, input }) => {
            const queue = createProductScraperQueue(ctx.db)
            return await queue.processNextJob(
                input?.workerId || `manual-${Date.now()}`,
                input?.options
            )
        }),

    resetStalledJobs: adminProcedure
        .input(
            z
                .object({
                    stalledMinutes: z.number().min(5).max(120).default(30)
                })
                .optional()
        )
        .mutation(async ({ ctx, input }) => {
            const queue = createProductScraperQueue(ctx.db)
            return await queue.resetStalledJobs(input?.stalledMinutes)
        }),

    getQueueStats: adminProcedure.query(async ({ ctx }) => {
        const queue = createProductScraperQueue(ctx.db)
        return await queue.getQueueStats()
    }),

    startWorker: adminProcedure
        .input(
            z
                .object({
                    workerId: z.string().optional(),
                    pollInterval: z.number().min(1000).max(60000).default(5000),
                    options: scraperOptionsSchema.optional()
                })
                .optional()
        )
        .mutation(async ({ ctx, input }) => {
            if (!worker) {
                worker = createScraperWorker(ctx.db)
            }

            if (worker.isRunning) {
                return { success: false, message: 'Worker is already running' }
            }

            const started = await worker.start({
                workerId: input?.workerId,
                pollInterval: input?.pollInterval,
                ...input?.options
            })

            return {
                success: started,
                message: started ? 'Worker started' : 'Failed to start worker'
            }
        }),

    stopWorker: adminProcedure.mutation(async () => {
        if (!worker || !worker.isRunning) {
            return { success: false, message: 'No worker is running' }
        }

        const stopped = worker.stop()
        return {
            success: stopped,
            message: stopped ? 'Worker stopped' : 'Failed to stop worker'
        }
    }),

    workerStatus: adminProcedure.query(() => {
        return {
            isRunning: worker?.isRunning || false
        }
    })
})
