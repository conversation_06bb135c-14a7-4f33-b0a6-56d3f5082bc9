import { type ScrapeAndCreateParams } from '../schemas'
import { type ProductScraper } from '../types'
import { navigateWithStealth, safelyCloseResources } from '../browser'
import { type CreateProduct } from '~/server/product/schemas'
import { parsePrice } from '../utils/price'
import { safelyGetText } from '../utils/humanBehavior'
import { generateProductUrl } from '~/server/product/url'

const scrapeProduct = async ({
    product,
    options
}: ScrapeAndCreateParams): Promise<CreateProduct> => {
    const productUrl = generateProductUrl(product.store, product.identifier)

    // Navigate to the product page with stealth
    const { browser, context, page } = await navigateWithStealth(
        productUrl,
        options
    )

    try {
        // Safely get the price text
        const priceText = await safelyGetText(page, '[data-ge-price="true"]', {
            timeout: options?.timeout
        })

        if (!priceText) {
            throw new Error('Price element not found')
        }

        const currentPrice = parsePrice(priceText)

        return {
            ...product,
            currentPrice
        }
    } finally {
        // Safely close all resources
        await safelyCloseResources(page, context, browser, options)
    }
}

export const prusa3dScraper: ProductScraper = {
    name: 'prusa3d',
    scrape: async (params: ScrapeAndCreateParams) => {
        const scrapedProduct = await scrapeProduct(params)
        return scrapedProduct
    }
}
