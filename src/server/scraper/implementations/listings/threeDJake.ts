import { type ListingScraper } from '../../types'
import { type CreateProduct } from '~/server/product/schemas'
import { navigateWithStealth, safelyCloseResources } from '../../browser'
import { type ScraperOptions } from '../../schemas'
import { Currency, MaterialType, Store, Location } from '@prisma/client'
import { parsePrice } from '../../utils/price'
import { addRandomDelay } from '../../utils/humanBehavior'

/**
 * Scraper for 3DJake product listings
 */
export const threeDJakeListingScraper: ListingScraper = {
    name: '3DJake Listing Scraper',
    store: Store.threeDJake,

    /**
     * Get the URLs of 3DJake filament listing pages to scrape
     */
    getListingUrls: () => [
        // Main filament category pages
        'https://www.3djake.de/filament/pet-filament'
    ],

    /**
     * Scrape 3DJake product listings to find new products
     */
    scrapeListings: async (
        options?: ScraperOptions
    ): Promise<CreateProduct[]> => {
        console.log(`[LISTINGS SCRAPER] Starting 3DJake listings scraper`)
        const products: CreateProduct[] = []
        const urls = threeDJakeListingScraper.getListingUrls()

        for (const url of urls) {
            try {
                const pageProducts = await scrapeListingPage(url, options)
                products.push(...pageProducts)

                // Add a delay between pages to avoid detection
                const delay = Math.floor(Math.random() * 5000) + 3000
                await new Promise((resolve) => setTimeout(resolve, delay))
            } catch (error) {
                console.error(
                    `[LISTINGS SCRAPER] Error scraping listing page ${url}:`,
                    error
                )
            }
        }

        console.log(
            `[LISTINGS SCRAPER] Completed scraping. Total products found: ${products.length}`
        )
        return products
    }
}

/**
 * Scrape a single 3DJake listing page
 */
async function scrapeListingPage(
    url: string,
    options?: ScraperOptions
): Promise<CreateProduct[]> {
    console.log(`[LISTINGS SCRAPER] Starting to scrape listing page: ${url}`)
    const products: CreateProduct[] = []

    // Navigate to the listing page with stealth
    const { browser, context, page } = await navigateWithStealth(url, options)

    try {
        // Wait for product grid to load
        await page.waitForSelector('#productList', {
            timeout: options?.timeout || 30000
        })

        // Process the first page
        await processProductsOnPage(page, products)
        console.log(
            `[LISTINGS SCRAPER] Found ${products.length} products on page 1`
        )

        // Check if there are more pages and process them
        let currentPage = 1
        const maxPages = 3 // Limit to 1 page to avoid too many requests

        while (currentPage < maxPages) {
            // Check if there's a next page button
            const hasNextPage = await page
                .locator('.pagination__btn--next')
                .isVisible()
                .catch(() => false)

            if (!hasNextPage) break

            // Click the next page button
            console.log(
                `[LISTINGS SCRAPER] Navigating to page ${currentPage + 1}`
            )
            await page.click('.pagination__btn--next').catch(() => {})

            // Wait for the page to load
            await page
                .waitForSelector('#productList', {
                    timeout: options?.timeout || 30000
                })
                .catch(() => {})

            // Wait for network to be idle
            await page.waitForLoadState('networkidle').catch(() => {})

            // Process products on this page
            const previousProductCount = products.length
            await processProductsOnPage(page, products)
            console.log(
                `[LISTINGS SCRAPER] Found ${products.length - previousProductCount} products on page ${currentPage + 1}`
            )

            currentPage++
        }

        console.log(
            `[LISTINGS SCRAPER] Finished processing all pages. Total products: ${products.length}`
        )
        return products
    } finally {
        // Safely close all resources
        await safelyCloseResources(page, context, browser, options)
    }
}

/**
 * Process all products on the current page
 */
async function processProductsOnPage(
    page: any,
    products: CreateProduct[]
): Promise<void> {
    // Get all product cards
    const productCards = await page.$$('#productList .productCard')
    console.log(
        `[LISTINGS SCRAPER] Found ${productCards.length} product cards to process`
    )

    // Get the current URL to determine material type
    const currentUrl = page.url()

    for (const card of productCards) {
        try {
            // Get the product link element
            const linkElement = await card.$(
                '.productCard__title .productCard__link'
            )

            if (!linkElement) continue

            // Extract the product URL to use as identifier
            const productUrl = await linkElement.getAttribute('href')
            if (!productUrl) continue

            // Remove protocol (http:// or https://) from the URL
            const identifier = productUrl.replace(/^https?:\/\//, '')

            // Extract full product name (including specifications)
            const fullName = await linkElement.innerText()
            if (!fullName) continue

            // Create a clean name by removing everything after the first comma
            // but keep the full name for extracting weight and other information
            let name = fullName
            const commaIndex = fullName.indexOf(',')
            if (commaIndex !== -1) {
                name = fullName.substring(0, commaIndex).trim()
            }

            // Extract brand name from the product card
            const brandElement = await card.$('.productCard__brand')
            const brand = brandElement
                ? await brandElement.innerText()
                : 'Unknown'

            // Get current price - check for reduced price first, then regular price
            const reducedPriceElement = await card.$(
                '.productCard__price .price--reduced'
            )
            const reducedPriceText = reducedPriceElement
                ? await reducedPriceElement.innerText()
                : ''

            // If no reduced price, look for regular price
            let priceText = reducedPriceText
            if (!priceText) {
                const regularPriceElement = await card.$(
                    '.productCard__price > span:first-child'
                )
                priceText = regularPriceElement
                    ? await regularPriceElement.innerText()
                    : ''
            }

            if (!priceText) continue
            const currentPrice = parsePrice(priceText)

            // Extract material type from the page URL
            let materialType: MaterialType = MaterialType.PLA
            if (currentUrl.includes('pet')) {
                materialType = MaterialType.PETG
            } else if (currentUrl.includes('abs')) {
                materialType = MaterialType.ABS
            } else if (currentUrl.includes('tpu')) {
                materialType = MaterialType.TPU
            }

            // Extract weight from full product name (usually in format like "1.75mm - 1kg")
            let weight = 1000 // Default to 1kg (1000g)

            // Match weight patterns with both dot and comma as decimal separators
            // Also handle European number format with dots as thousands separators (e.g., "1.000 g")
            const kgMatch = fullName.match(/(\d+(?:[.,]\d+)?)(?:\s*)kg/i)
            const gMatch = fullName.match(/(\d+(?:[.,]\d+)?)(?:\s*)g/i)

            if (kgMatch && kgMatch[1]) {
                // Process the matched weight string to handle European number formats
                const weightStr = kgMatch[1]
                    .replace(/\.(?=\d{3})/g, '') // Remove dots used as thousands separators
                    .replace(',', '.') // Replace comma with dot for decimal point
                weight = Math.round(parseFloat(weightStr) * 1000)
            } else if (gMatch && gMatch[1]) {
                // Process the matched weight string to handle European number formats
                const weightStr = gMatch[1]
                    .replace(/\.(?=\d{3})/g, '') // Remove dots used as thousands separators
                    .replace(',', '.') // Replace comma with dot for decimal point
                weight = Math.round(parseFloat(weightStr))
            }

            // Extract color from product name
            // The color is typically the text after the filament type (PLA, PETG, etc.) and before the comma
            let color = 'Unknown'

            // Use a regex to match everything between a filament type and a comma
            const colorMatch = fullName.match(
                /(PLA|PETG|ABS|TPU|ASA|NYLON)\s+([^,]+)/i
            )

            if (colorMatch && colorMatch[2]) {
                color = colorMatch[2].trim()
                // Capitalize the first letter using modern JavaScript
                color = color.replace(/^\w/, (c) => c.toUpperCase())
            }

            // Create product object with only the properties defined in CreateProduct type
            products.push({
                name,
                identifier,
                store: Store.threeDJake,
                brand,
                currentPrice,
                currency: Currency.EUR,
                materialType,
                weight,
                color,
                locations: [Location.Germany]
            })

            // Add a small delay between processing products
            await addRandomDelay(page, 100, 300)
        } catch (error) {
            console.error(
                `[LISTINGS SCRAPER] Error extracting product data:`,
                error
            )
        }
    }
}
