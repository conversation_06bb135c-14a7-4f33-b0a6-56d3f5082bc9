import { type ScrapeAndCreateParams } from '../schemas'
import { type ProductScraper } from '../types'
import { createStealthPage, closeBrowser } from '../browser'
import { type CreateProduct } from '~/server/product/schemas'
import { parsePrice } from '../utils/price'
import {
    simulateHumanBehavior,
    addRandomDelay,
    detectAntiBotMeasures
} from '../utils/humanBehavior'
import { generateProductUrl } from '~/server/product/url'

const scrapeProduct = async ({
    product,
    options
}: ScrapeAndCreateParams): Promise<CreateProduct> => {
    const productUrl = generateProductUrl(product.store, product.identifier)

    // Create a stealth page with all anti-detection measures
    const { browser, context, page } = await createStealthPage(options)

    try {
        // Add random delay before navigation to mimic human behavior
        await addRandomDelay(
            page,
            options?.delayMin || 1000,
            options?.delayMax || 3000
        )

        // Navigate with a more realistic waitUntil option
        await page.goto(productUrl, {
            waitUntil: 'domcontentloaded',
            timeout: options?.timeout || 30000
        })

        // Wait for the page to be fully loaded
        await page.waitForLoadState('networkidle').catch(() => {
            console.log('Network did not become idle, continuing anyway')
        })

        // Simulate human-like behavior (handles cookie consent, scrolling, etc.)
        await simulateHumanBehavior(page)

        // Wait for price element to be available
        await page
            .waitForSelector('#corePriceDisplay_desktop_feature_div .a-price', {
                timeout: options?.timeout || 30000,
                state: 'attached'
            })
            .catch(() => {
                console.log(
                    'Price selector timeout, will try to extract anyway'
                )
            })

        // Get the price text
        const priceText = await page
            .locator('#corePriceDisplay_desktop_feature_div .a-price')
            .first()
            .textContent()
            .catch(() => null)

        if (!priceText) {
            // Check if we're being blocked by anti-bot measures
            if (await detectAntiBotMeasures(page)) {
                throw new Error('Access blocked - detected anti-bot measures')
            }

            throw new Error('Price element not found')
        }

        const currentPrice = parsePrice(priceText)

        return {
            ...product,
            currentPrice
        }
    } finally {
        // Add a random delay before closing to appear more natural
        await addRandomDelay(page, 500, 2000)

        await page.close()
        await context.close()
        await closeBrowser(browser)
    }
}

export const amazonScraper: ProductScraper = {
    name: 'amazon',
    scrape: async (params: ScrapeAndCreateParams) => {
        const scrapedProduct = await scrapeProduct(params)
        return scrapedProduct
    }
}
