import { type Browser } from 'playwright'
import {
    type ScrapeAndCreateParams,
    type ScraperOptions,
    type AiScraperConfig
} from './schemas'
import type { CreateProduct } from '../product/schemas'
import { type Store } from '@prisma/client'

/**
 * Interface for product scrapers that update existing products
 */
export type ProductScraper = {
    name: string
    scrape: (props: ScrapeAndCreateParams) => Promise<CreateProduct>
}

// Keep the old type for backward compatibility
export type Scraper = ProductScraper

export type ScraperContext = {
    browser: Browser | null
}

export type ProductScraperError = {
    message: string
    scraper: string
    identifier?: string
    cause?: unknown
}

// Keep the old type for backward compatibility
export type ScraperError = ProductScraperError

export const createProductScraperError = (
    message: string,
    scraper: string,
    idenfitier?: string,
    cause?: unknown
): ProductScraperError => ({
    message,
    scraper,
    identifier: idenfitier,
    cause
})

// Keep the old function for backward compatibility
export const createScraperError = createProductScraperError

/**
 * Interface for listing scrapers that can find and add new products
 */
export type ListingScraper = {
    name: string
    store: Store
    /**
     * Scrape a product listing page to find new products
     * @param options Scraper options
     * @returns Array of product data objects ready to be created in the database
     */
    scrapeListings: (options?: ScraperOptions) => Promise<CreateProduct[]>
    /**
     * Get the URLs of listing pages to scrape
     * @returns Array of URLs to scrape
     */
    getListingUrls: () => string[]
}

/**
 * Parameters for scraping a listing page
 */
export type ScrapeListingParams = {
    url: string
    options?: ScraperOptions
}

/**
 * Result of a listing scrape operation
 */
export type ListingScrapeResult = {
    store: Store
    productsFound: number
    productsAdded: number
    errors: string[]
}

/**
 * Interface for AI-powered generic listing scrapers
 */
export type AiListingScraper = {
    name: string
    /** Configuration for the AI scraper */
    config: AiScraperConfig
    /**
     * Scrape any webpage using AI to extract product information
     * @param url The URL to scrape
     * @param options Scraper options
     * @returns Array of product data objects ready to be created in the database
     */
    scrapeUrl: (
        url: string,
        options?: ScraperOptions
    ) => Promise<CreateProduct[]>
}
