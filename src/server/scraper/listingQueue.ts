import { type PrismaClient, JobStatus, type Store } from '@prisma/client'
import { createListingScraperService } from './listingService'
import { listingScraperRegistry } from './listingRegistry'
import { type ScraperOptions } from './schemas'

// Define a type for the ListingScrapeJob model
interface ListingScrapeJobModel {
    id: string
    store: Store
    status: JobStatus
    priority: number
    scheduledFor: Date
    lastAttemptedAt?: Date | null
    nextAttemptAt?: Date | null
    attempts: number
    maxAttempts: number
    createdAt: Date
    completedAt?: Date | null
    errorMessage?: string | null
    lockedBy?: string | null
    lockedAt?: Date | null
}

// Type for queue stats
interface QueueStat {
    status: JobStatus
    _count: {
        _all: number
    }
}

// Type assertion to handle the fact that Prisma client might not have been generated yet
// This is a temporary workaround until the migration is applied
type ExtendedPrismaClient = PrismaClient & {
    listingScrapeJob: {
        findMany: (args: any) => Promise<ListingScrapeJobModel[]>
        findFirst: (args: any) => Promise<ListingScrapeJobModel | null>
        create: (args: any) => Promise<ListingScrapeJobModel>
        createMany: (args: any) => Promise<{ count: number }>
        update: (args: any) => Promise<ListingScrapeJobModel>
        updateMany: (args: any) => Promise<{ count: number }>
        groupBy: (args: any) => Promise<QueueStat[]>
    }
}

// Store cooldowns in milliseconds (to avoid hitting rate limits)
const STORE_COOLDOWNS: Record<Store, number> = {
    amazon: 60000, // 1 minute
    prusa3d: 30000, // 30 seconds
    threeDJake: 45000 // 45 seconds
}

/**
 * Create a queue for listing scraper jobs
 */
export const createListingScraperQueue = (prismaDb: PrismaClient) => {
    // Cast the PrismaClient to our extended type to handle the new model
    const db = prismaDb as unknown as ExtendedPrismaClient

    // Map to track the last attempt time for each store
    const storeLastAttemptMap = new Map<Store, Date>()

    /**
     * Queue listing scraper jobs for all stores
     */
    const queueAllStores = async (priority = 1) => {
        const stores = listingScraperRegistry.getAllStores()

        // Check which stores already have pending jobs
        const existingJobs = await db.listingScrapeJob.findMany({
            where: {
                store: { in: stores },
                status: { in: [JobStatus.pending, JobStatus.processing] }
            },
            select: { store: true }
        })

        const existingStores = new Set(
            existingJobs.map((job: any) => job.store)
        )
        const newStores = stores.filter((store) => !existingStores.has(store))

        if (newStores.length === 0) {
            return 0
        }

        // Create jobs for stores that don't have pending jobs
        const jobs = await db.listingScrapeJob.createMany({
            data: newStores.map((store) => ({
                store,
                priority,
                status: JobStatus.pending,
                scheduledFor: new Date()
            })),
            skipDuplicates: true
        })

        return jobs.count
    }

    /**
     * Queue a listing scraper job for a specific store
     */
    const queueStore = async (store: Store, priority = 1) => {
        // Check if the store has a listing scraper
        const scraper = listingScraperRegistry.get(store)
        if (!scraper) {
            throw new Error(`No listing scraper found for store: ${store}`)
        }

        // Check if the store already has a pending job
        const existingJob = await db.listingScrapeJob.findFirst({
            where: {
                store,
                status: { in: [JobStatus.pending, JobStatus.processing] }
            }
        })

        if (existingJob) {
            return { added: false, message: 'Store already has a pending job' }
        }

        // Add the job to the queue
        await db.listingScrapeJob.create({
            data: {
                store,
                priority,
                status: JobStatus.pending,
                scheduledFor: new Date()
            }
        })

        return { added: true, message: 'Job added to queue' }
    }

    /**
     * Claim the next job from the queue
     */
    const claimNextJob = async (workerId: string) => {
        const now = new Date()

        // Start a transaction to ensure atomic job claiming
        return await db.$transaction(async (tx) => {
            // Cast the transaction to our extended type
            const txWithListingScrapeJob = tx as unknown as ExtendedPrismaClient

            // Find all pending jobs
            const pendingJobs =
                await txWithListingScrapeJob.listingScrapeJob.findMany({
                    where: {
                        status: JobStatus.pending,
                        scheduledFor: { lte: now },
                        OR: [
                            { lockedAt: null },
                            {
                                lockedAt: {
                                    lt: new Date(now.getTime() - 5 * 60 * 1000)
                                }
                            } // Consider locks older than 5 minutes as stale
                        ]
                    },
                    orderBy: [{ priority: 'desc' }, { createdAt: 'asc' }],
                    take: 50 // Get a batch to filter
                })

            // Filter jobs based on store cooldowns
            const eligibleJobs = pendingJobs.filter(
                (job: ListingScrapeJobModel) => {
                    const store = job.store as Store
                    const lastAttempt = storeLastAttemptMap.get(store)

                    if (!lastAttempt) return true // No previous attempt for this store

                    const cooldown =
                        STORE_COOLDOWNS[
                            store as keyof typeof STORE_COOLDOWNS
                        ] || 5000 // Default to 5 seconds
                    const timeSinceLastAttempt =
                        now.getTime() - lastAttempt.getTime()

                    return timeSinceLastAttempt > cooldown
                }
            )

            if (eligibleJobs.length === 0) {
                return null
            }

            // Take the highest priority job
            const job = eligibleJobs[0]

            if (!job) {
                return null
            }

            // Update the job status
            await txWithListingScrapeJob.listingScrapeJob.update({
                where: { id: job.id },
                data: {
                    status: JobStatus.processing,
                    lockedBy: workerId,
                    lockedAt: now,
                    lastAttemptedAt: now,
                    attempts: { increment: 1 }
                }
            })

            return job
        })
    }

    /**
     * Complete a job with success or failure
     */
    const completeJob = async (
        jobId: string,
        success: boolean,
        errorMessage?: string
    ) => {
        return await db.listingScrapeJob.update({
            where: { id: jobId },
            data: {
                status: success ? JobStatus.completed : JobStatus.failed,
                completedAt: new Date(),
                errorMessage: errorMessage || null,
                lockedBy: null,
                lockedAt: null
            }
        })
    }

    /**
     * Process the next job in the queue
     */
    const processNextJob = async (
        workerId: string,
        options?: ScraperOptions
    ) => {
        const job = await claimNextJob(workerId)

        if (!job) {
            return null
        }

        try {
            // Update the last attempt time for this store
            storeLastAttemptMap.set(job.store, new Date())

            const listingScraperService = createListingScraperService(db)
            const result = await listingScraperService.scrapeStoreListings(
                job.store,
                options
            )

            await completeJob(job.id, result.errors.length === 0)
            return result
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error)
            await completeJob(job.id, false, errorMessage)
            return null
        }
    }

    /**
     * Reset stalled jobs
     */
    const resetStalledJobs = async (stalledMinutes = 30) => {
        const stalledBefore = new Date(Date.now() - stalledMinutes * 60 * 1000)

        return await db.listingScrapeJob.updateMany({
            where: {
                status: JobStatus.processing,
                lockedAt: { lt: stalledBefore }
            },
            data: {
                status: JobStatus.pending,
                lockedBy: null,
                lockedAt: null,
                scheduledFor: new Date()
            }
        })
    }

    /**
     * Get queue statistics
     */
    const getQueueStats = async () => {
        const stats = await db.listingScrapeJob.groupBy({
            by: ['status'],
            _count: {
                _all: true
            }
        })

        return stats.map((stat: QueueStat) => ({
            status: stat.status,
            count: stat._count._all
        }))
    }

    return {
        queueAllStores,
        queueStore,
        processNextJob,
        resetStalledJobs,
        getQueueStats
    }
}

export type ListingScraperQueue = ReturnType<typeof createListingScraperQueue>
