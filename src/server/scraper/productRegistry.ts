import { type ProductScraper } from './types'
import { amazonScraper } from './implementations/amazon'
import { prusa3dScraper } from './implementations/prusa3d'
import { threeDJakeScraper } from './implementations/threeDJake'
import { Store } from '@prisma/client'

const productScrapers = new Map<Store, ProductScraper>([
    [Store.amazon, amazonScraper],
    [Store.prusa3d, prusa3dScraper],
    [Store.threeDJake, threeDJakeScraper]
])

export const productScraperRegistry = {
    get: (store: Store) => productScrapers.get(store)
}

// Keep the old registry for backward compatibility
export const scraperRegistry = productScraperRegistry
