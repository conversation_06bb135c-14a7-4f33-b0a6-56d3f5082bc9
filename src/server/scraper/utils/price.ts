export function parsePrice(input: string | null): number {
    if (!input?.trim()) {
        throw new Error('Price string is empty or null')
    }

    // First remove any currency symbols and whitespace
    const cleaned = input.trim().replace(/[^0-9,.]/g, '')

    // Determine which separator is the decimal point
    // If there's a comma followed by exactly 2 digits at the end, it's the decimal separator
    const useCommaAsDecimal = /,\d{2}$/.test(cleaned)

    // Replace the non-decimal separator with empty string and convert decimal to dot
    const normalized = useCommaAsDecimal
        ? cleaned.replace(/\./g, '').replace(',', '.')
        : cleaned.replace(/,/g, '')

    const [whole = '0', fraction = '00'] = normalized.split('.')

    const wholeCents = parseInt(whole, 10) * 100
    const fractionalCents = parseInt(fraction.padEnd(2, '0').slice(0, 2), 10)

    return wholeCents + fractionalCents
}
