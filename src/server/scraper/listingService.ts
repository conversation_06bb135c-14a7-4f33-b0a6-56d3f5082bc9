import { type PrismaClient, type Store } from '@prisma/client'
import { type ListingScraper, type ListingScrapeResult } from './types'
import { listingScraperRegistry } from './listingRegistry'
import { createProductService } from '../product/service'
import { type ScraperOptions } from './schemas'

/**
 * Create a service for managing listing scrapers
 */
export const createListingScraperService = (db: PrismaClient) => {
    /**
     * Scrape listings for a specific store
     */
    const scrapeStoreListings = async (
        store: Store,
        options?: ScraperOptions
    ): Promise<ListingScrapeResult> => {
        const scraper = listingScraperRegistry.get(store)
        if (!scraper) {
            return {
                store,
                productsFound: 0,
                productsAdded: 0,
                errors: [`No listing scraper found for store: ${store}`]
            }
        }

        try {
            // Get products from the listing pages
            const newProducts = await scraper.scrapeListings(options)

            if (newProducts.length === 0) {
                return {
                    store,
                    productsFound: 0,
                    productsAdded: 0,
                    errors: []
                }
            }

            // Create or update the products in the database
            const productService = createProductService(db)
            const errors: string[] = []
            let addedCount = 0

            // Process each product
            for (const product of newProducts) {
                try {
                    // Use upsert to create or update the product
                    const result = await productService.upsert(product)

                    // If the product was created (not updated), increment the counter
                    if (
                        !result.updatedAt ||
                        result.createdAt.getTime() ===
                            result.updatedAt.getTime()
                    ) {
                        addedCount++
                    }
                } catch (error) {
                    const errorMessage =
                        error instanceof Error ? error.message : String(error)
                    errors.push(
                        `Failed to add product ${product.identifier}: ${errorMessage}`
                    )
                }
            }

            return {
                store,
                productsFound: newProducts.length,
                productsAdded: addedCount,
                errors
            }
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error)
            return {
                store,
                productsFound: 0,
                productsAdded: 0,
                errors: [
                    `Failed to scrape listings for ${store}: ${errorMessage}`
                ]
            }
        }
    }

    /**
     * Scrape listings for all stores
     */
    const scrapeAllListings = async (
        options?: ScraperOptions
    ): Promise<ListingScrapeResult[]> => {
        const stores = listingScraperRegistry.getAllStores()
        const results: ListingScrapeResult[] = []

        for (const store of stores) {
            const result = await scrapeStoreListings(store, options)
            results.push(result)
        }

        return results
    }

    return {
        scrapeStoreListings,
        scrapeAllListings
    }
}

export type ListingScraperService = ReturnType<
    typeof createListingScraperService
>
