import { type ListingScraper } from './types'
import { Store } from '@prisma/client'
import { threeDJakeListingScraper } from './implementations/listings/threeDJake'
import { prusa3dListingScraper } from './implementations/listings/prusa3d'

// Map of store to listing scraper implementation
const listingScrapers = new Map<Store, ListingScraper>([
    [Store.threeDJake, threeDJakeListingScraper],
    [Store.prusa3d, prusa3dListingScraper]
    // Add more listing scrapers as they are implemented
    // [Store.amazon, amazonListingScraper],
])

/**
 * Registry for listing scrapers
 */
export const listingScraperRegistry = {
    /**
     * Get a listing scraper for a specific store
     */
    get: (store: Store) => listingScrapers.get(store),

    /**
     * Get all stores that have listing scrapers
     */
    getAllStores: () => Array.from(listingScrapers.keys())
}
