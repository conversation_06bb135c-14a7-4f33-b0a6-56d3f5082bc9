import { PrismaClient } from '@prisma/client'
import { createProductScraperQueue } from './productQueue'
import type { ScraperOptions } from './schemas'

export const createScraperWorker = (db: PrismaClient) => {
    const queue = createProductScraperQueue(db)
    let isRunning = false
    let stopRequested = false

    /**
     * Start the worker process
     */
    const start = async (
        options?: Partial<ScraperOptions> & {
            workerId?: string
            pollInterval?: number
        }
    ) => {
        if (isRunning) {
            return false
        }

        const workerId =
            options?.workerId ||
            `worker-${Math.random().toString(36).substring(2, 9)}`
        const pollInterval = options?.pollInterval || 5000

        console.log(`Starting scraper worker ${workerId}`)
        isRunning = true
        stopRequested = false

        // Reset any stalled jobs before starting
        await queue.resetStalledJobs()

        // Main worker loop
        while (isRunning && !stopRequested) {
            try {
                const scraperOptions: ScraperOptions | undefined = options
                    ? {
                          headless: options.headless ?? true,
                          timeout: options.timeout ?? 30000,
                          retries: options.retries ?? 3,
                          httpCredentials: options.httpCredentials,
                          delayMin: options.delayMin ?? 500,
                          delayMax: options.delayMax ?? 3000
                      }
                    : undefined

                const result = await queue.processNextJob(
                    workerId,
                    scraperOptions
                )

                if (!result) {
                    // No jobs processed, wait before polling again
                    await new Promise((resolve) =>
                        setTimeout(resolve, pollInterval)
                    )
                }
            } catch (error) {
                console.error(`Worker ${workerId} encountered an error:`, error)
                // Wait before retrying
                await new Promise((resolve) =>
                    setTimeout(resolve, pollInterval)
                )
            }
        }

        isRunning = false
        console.log(`Scraper worker ${workerId} stopped`)
        return true
    }

    /**
     * Stop the worker process
     */
    const stop = () => {
        if (!isRunning) {
            return false
        }

        stopRequested = true
        return true
    }

    return {
        start,
        stop,
        get isRunning() {
            return isRunning
        }
    }
}

export type ScraperWorker = ReturnType<typeof createScraperWorker>
