import { type PrismaClient } from '@prisma/client'
import {
    type ScrapeAllRequest,
    type ScrapeAndCreateParams,
    type ScrapeByIdRequest
} from './schemas'
import { productScraperRegistry } from './productRegistry'
import { createProductScraperError } from './types'
import { createProductService } from '../product/service'

export const createProductScrapingService = (db: PrismaClient) => {
    const scrapeAndCreate = async ({
        product,
        options
    }: ScrapeAndCreateParams) => {
        const scraper = productScraperRegistry.get(product.store)
        if (!scraper) {
            throw createProductScraperError(
                `No scraper found for store: ${product.store}`,
                product.store,
                product.identifier
            )
        }

        const scrapedProduct = await scraper.scrape({
            product,
            options
        })
        if (!scrapedProduct) {
            throw createProductScraperError(
                'No product data found',
                product.store,
                product.identifier
            )
        }

        const productService = createProductService(db)
        return await productService.create(scrapedProduct)
    }

    const scrapeAllProducts = async ({ options }: ScrapeAllRequest) => {
        const products = await db.product.findMany({
            select: {
                name: true,
                identifier: true,
                store: true,
                brand: true,
                currentPrice: true,
                currency: true,
                materialType: true,
                weight: true,
                color: true,
                locations: true
            }
        })

        if (products.length === 0) {
            return []
        }

        const results = []
        for (const product of products) {
            const scraper = productScraperRegistry.get(product.store)
            if (!scraper) {
                console.error(`No scraper found for store: ${product.store}`)
                continue
            }

            try {
                const scrapedProduct = await scraper.scrape({
                    product,
                    options
                })
                const productService = createProductService(db)
                const stored = await productService.upsert(scrapedProduct)
                results.push(stored)
            } catch (error) {
                console.error(`Failed to scrape ${product.identifier}:`, error)
            }
        }

        return results
    }

    const scrapeById = async ({ id, options }: ScrapeByIdRequest) => {
        // get the product by id from the database
        const product = await db.product.findUnique({
            where: {
                id
            }
        })

        if (!product) {
            throw createProductScraperError(`No product found with id: ${id}`, '', id)
        }
        const scraper = productScraperRegistry.get(product?.store)

        if (!scraper) {
            throw createProductScraperError(
                `No scraper found for store: ${product?.store}`,
                product?.store,
                id
            )
        }

        const scrapedProduct = await scraper.scrape({
            product,
            options
        })

        const productService = createProductService(db)
        return await productService.upsert(scrapedProduct)
    }

    return { scrapeAllProducts, scrapeAndCreate, scrapeById }
}

// Keep the old service for backward compatibility
export const createScrapingService = createProductScrapingService

export type ProductScrapingService = ReturnType<
    typeof createProductScrapingService
>
export type ScrapingService = ProductScrapingService
