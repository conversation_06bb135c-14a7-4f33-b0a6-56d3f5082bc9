import { useState } from 'react'
import Head from 'next/head'
import {
    Container,
    <PERSON>ing,
    Box,
    Stack,
    Card,
    Text,
    Flex,
    Stat,
    Badge,
    HStack,
    IconButton,
    Input,
    Button
} from '@chakra-ui/react'
import { api } from '~/utils/api'
import { FilaFindIcon } from '~/components/FilaFindIcon'
import { FaPlay, FaStop, FaSync, FaPlus, FaArrowRight } from 'react-icons/fa'
import { JobStatus } from '@prisma/client'
import Link from 'next/link'
import { Tooltip } from '~/components/ui/tooltip'
import { toaster } from '~/components/ui/toaster'

// Type for scraper options
interface ScraperOptions {
    headless: boolean
    timeout: number
    retries: number
    delayMin: number
    delayMax: number
}

export default function ScraperAdmin() {
    // Create a custom toast function to handle the toaster API
    const showToast = (props: {
        title: string
        description?: string
        status: 'success' | 'error' | 'info'
    }) => {
        toaster.create({
            title: props.title,
            description: props.description,
            type: props.status,
            duration: 3000
        })
    }

    // Worker state
    const { data: workerStatus, refetch: refetchWorkerStatus } =
        api.scraper.workerStatus.useQuery(undefined, { refetchInterval: 5000 })

    // Queue stats
    const { data: queueStats, refetch: refetchQueueStats } =
        api.scraper.getQueueStats.useQuery(undefined, { refetchInterval: 5000 })

    // Mutations
    const startWorkerMutation = api.scraper.startWorker.useMutation({
        onSuccess: (data) => {
            if (data.success) {
                showToast({
                    title: 'Worker started',
                    status: 'success'
                })
                refetchWorkerStatus()
            } else {
                showToast({
                    title: 'Failed to start worker',
                    description: data.message,
                    status: 'error'
                })
            }
        },
        onError: (error) => {
            showToast({
                title: 'Error',
                description: error.message,
                status: 'error'
            })
        }
    })

    const stopWorkerMutation = api.scraper.stopWorker.useMutation({
        onSuccess: (data) => {
            if (data.success) {
                showToast({
                    title: 'Worker stopped',
                    status: 'success'
                })
                refetchWorkerStatus()
            } else {
                showToast({
                    title: 'Failed to stop worker',
                    description: data.message,
                    status: 'error'
                })
            }
        },
        onError: (error) => {
            showToast({
                title: 'Error',
                description: error.message,
                status: 'error'
            })
        }
    })

    const queueAllProductsMutation = api.scraper.queueAllProducts.useMutation({
        onSuccess: (count) => {
            showToast({
                title: 'Products queued',
                description: `Added ${count} products to the queue`,
                status: 'success'
            })
            refetchQueueStats()
        },
        onError: (error) => {
            showToast({
                title: 'Error',
                description: error.message,
                status: 'error'
            })
        }
    })

    const queueProductMutation = api.scraper.queueProduct.useMutation({
        onSuccess: (result) => {
            showToast({
                title: result.added ? 'Product queued' : 'Product not queued',
                description: result.message,
                status: result.added ? 'success' : 'info'
            })
            refetchQueueStats()
        },
        onError: (error) => {
            showToast({
                title: 'Error',
                description: error.message,
                status: 'error'
            })
        }
    })

    const processNextJobMutation = api.scraper.processNextJob.useMutation({
        onSuccess: (data) => {
            if (data) {
                showToast({
                    title: 'Job processed',
                    description: `Processed ${data.name}`,
                    status: 'success'
                })
            } else {
                showToast({
                    title: 'No job to process',
                    description: 'Queue is empty or all jobs are on cooldown',
                    status: 'info'
                })
            }
            refetchQueueStats()
        },
        onError: (error) => {
            showToast({
                title: 'Error',
                description: error.message,
                status: 'error'
            })
        }
    })

    const resetStalledJobsMutation = api.scraper.resetStalledJobs.useMutation({
        onSuccess: (data) => {
            showToast({
                title: 'Stalled jobs reset',
                description: `Reset ${data.count} stalled jobs`,
                status: 'success'
            })
            refetchQueueStats()
        },
        onError: (error) => {
            showToast({
                title: 'Error',
                description: error.message,
                status: 'error'
            })
        }
    })

    // Scraper options state
    const [scraperOptions, setScraperOptions] = useState<ScraperOptions>({
        headless: true,
        timeout: 30000,
        retries: 3,
        delayMin: 500,
        delayMax: 3000
    })

    // Poll interval state
    const pollInterval = 5000

    // Product ID state for single product queueing
    const [productId, setProductId] = useState('')

    // Calculate total jobs and percentages
    const totalJobs =
        queueStats?.reduce((sum, stat) => sum + stat.count, 0) || 0
    const getJobCount = (status: JobStatus) =>
        queueStats?.find((stat) => stat.status === status)?.count || 0

    const pendingCount = getJobCount(JobStatus.pending)
    const processingCount = getJobCount(JobStatus.processing)
    const completedCount = getJobCount(JobStatus.completed)
    const failedCount = getJobCount(JobStatus.failed)

    const pendingPercent = totalJobs > 0 ? (pendingCount / totalJobs) * 100 : 0
    const processingPercent =
        totalJobs > 0 ? (processingCount / totalJobs) * 100 : 0
    const completedPercent =
        totalJobs > 0 ? (completedCount / totalJobs) * 100 : 0
    const failedPercent = totalJobs > 0 ? (failedCount / totalJobs) * 100 : 0

    // Handle starting the worker
    const handleStartWorker = () => {
        startWorkerMutation.mutate({
            pollInterval,
            options: scraperOptions
        })
    }

    // Handle stopping the worker
    const handleStopWorker = () => {
        stopWorkerMutation.mutate()
    }

    // Handle queueing all products
    const handleQueueAllProducts = () => {
        queueAllProductsMutation.mutate({ priority: 1 })
    }

    // Handle queueing a single product
    const handleQueueProduct = () => {
        if (!productId.trim()) {
            showToast({
                title: 'Error',
                description: 'Please enter a product ID',
                status: 'error'
            })
            return
        }

        queueProductMutation.mutate({
            productId: productId.trim(),
            priority: 1
        })

        // Clear the input after submission
        setProductId('')
    }

    // Handle processing the next job
    const handleProcessNextJob = () => {
        processNextJobMutation.mutate({
            workerId: `manual-${Date.now()}`,
            options: scraperOptions
        })
    }

    // Handle resetting stalled jobs
    const handleResetStalledJobs = () => {
        resetStalledJobsMutation.mutate({ stalledMinutes: 30 })
    }

    return (
        <>
            <Head>
                <title>Scraper Admin - FilaFind</title>
                <meta
                    name="description"
                    content="Scraper administration for FilaFind"
                />
            </Head>
            <Container maxW="container.xl" py={8}>
                {/* Header */}
                <Flex justifyContent="space-between" alignItems="center" mb={6}>
                    <HStack gap={2} alignItems="center">
                        <Link href="/" passHref>
                            <HStack gap={2} alignItems="center">
                                <FilaFindIcon size="xl" color="primary.400" />
                                <Heading
                                    as="h1"
                                    size="lg"
                                    color="primary.400"
                                    fontWeight="medium"
                                >
                                    filafind
                                </Heading>
                            </HStack>
                        </Link>
                        <Text color="gray.500" fontSize="sm">
                            scraper admin
                        </Text>
                    </HStack>
                </Flex>

                {/* Main Content */}
                <Stack gap={6}>
                    {/* Worker Status Section */}
                    <Card.Root>
                        <Card.Body>
                            <Flex
                                justifyContent="space-between"
                                alignItems="center"
                                mb={4}
                            >
                                <Heading as="h2" size="md">
                                    Worker Status
                                </Heading>
                                <HStack>
                                    <Badge
                                        colorScheme={
                                            workerStatus?.isRunning
                                                ? 'green'
                                                : 'gray'
                                        }
                                        fontSize="sm"
                                        px={2}
                                        py={1}
                                        borderRadius="md"
                                    >
                                        {workerStatus?.isRunning
                                            ? 'Running'
                                            : 'Stopped'}
                                    </Badge>
                                    {workerStatus?.isRunning ? (
                                        <Tooltip content="Stop Worker">
                                            <IconButton
                                                aria-label="Stop Worker"
                                                colorScheme="red"
                                                size="sm"
                                                onClick={handleStopWorker}
                                                disabled={
                                                    stopWorkerMutation.isPending
                                                }
                                            >
                                                <FaStop />
                                            </IconButton>
                                        </Tooltip>
                                    ) : (
                                        <Tooltip content="Start Worker">
                                            <IconButton
                                                aria-label="Start Worker"
                                                colorScheme="green"
                                                size="sm"
                                                onClick={handleStartWorker}
                                                disabled={
                                                    startWorkerMutation.isPending
                                                }
                                            >
                                                <FaPlay />
                                            </IconButton>
                                        </Tooltip>
                                    )}
                                </HStack>
                            </Flex>

                            <Flex alignItems="center" mb={2}>
                                <Box mr={2}>
                                    <input
                                        type="checkbox"
                                        id="headless-mode"
                                        checked={scraperOptions.headless}
                                        onChange={(e) =>
                                            setScraperOptions({
                                                ...scraperOptions,
                                                headless: e.target.checked
                                            })
                                        }
                                    />
                                </Box>
                                <Text fontSize="sm">Headless Mode</Text>
                            </Flex>

                            <Text fontSize="sm" color="gray.500" mt={2}>
                                Poll Interval: {pollInterval}ms
                            </Text>
                        </Card.Body>
                    </Card.Root>

                    {/* Queue Statistics Section */}
                    <Card.Root>
                        <Card.Body>
                            <Heading as="h2" size="md" mb={4}>
                                Queue Statistics
                            </Heading>

                            <Stack gap={3}>
                                <Flex justifyContent="space-between">
                                    <Text>Total Jobs:</Text>
                                    <Text fontWeight="bold">{totalJobs}</Text>
                                </Flex>

                                <Box>
                                    <Flex justifyContent="space-between" mb={1}>
                                        <Text fontSize="sm">Pending:</Text>
                                        <Text fontSize="sm">
                                            {pendingCount} (
                                            {pendingPercent.toFixed(1)}%)
                                        </Text>
                                    </Flex>
                                    <Box
                                        h="2"
                                        w="full"
                                        bg="blue.100"
                                        borderRadius="md"
                                        mb={2}
                                    >
                                        <Box
                                            h="full"
                                            w={`${pendingPercent}%`}
                                            bg="blue.500"
                                            borderRadius="md"
                                        />
                                    </Box>

                                    <Flex justifyContent="space-between" mb={1}>
                                        <Text fontSize="sm">Processing:</Text>
                                        <Text fontSize="sm">
                                            {processingCount} (
                                            {processingPercent.toFixed(1)}%)
                                        </Text>
                                    </Flex>
                                    <Box
                                        h="2"
                                        w="full"
                                        bg="yellow.100"
                                        borderRadius="md"
                                        mb={2}
                                    >
                                        <Box
                                            h="full"
                                            w={`${processingPercent}%`}
                                            bg="yellow.500"
                                            borderRadius="md"
                                        />
                                    </Box>

                                    <Flex justifyContent="space-between" mb={1}>
                                        <Text fontSize="sm">Completed:</Text>
                                        <Text fontSize="sm">
                                            {completedCount} (
                                            {completedPercent.toFixed(1)}%)
                                        </Text>
                                    </Flex>
                                    <Box
                                        h="2"
                                        w="full"
                                        bg="green.100"
                                        borderRadius="md"
                                        mb={2}
                                    >
                                        <Box
                                            h="full"
                                            w={`${completedPercent}%`}
                                            bg="green.500"
                                            borderRadius="md"
                                        />
                                    </Box>

                                    <Flex justifyContent="space-between" mb={1}>
                                        <Text fontSize="sm">Failed:</Text>
                                        <Text fontSize="sm">
                                            {failedCount} (
                                            {failedPercent.toFixed(1)}%)
                                        </Text>
                                    </Flex>
                                    <Box
                                        h="2"
                                        w="full"
                                        bg="red.100"
                                        borderRadius="md"
                                        mb={2}
                                    >
                                        <Box
                                            h="full"
                                            w={`${failedPercent}%`}
                                            bg="red.500"
                                            borderRadius="md"
                                        />
                                    </Box>
                                </Box>
                            </Stack>

                            <HStack mt={4} gap={2}>
                                <Tooltip content="Queue All Products">
                                    <IconButton
                                        aria-label="Queue All Products"
                                        onClick={handleQueueAllProducts}
                                        disabled={
                                            queueAllProductsMutation.isPending
                                        }
                                        size="sm"
                                    >
                                        <FaPlus />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip content="Process Next Job">
                                    <IconButton
                                        aria-label="Process Next Job"
                                        onClick={handleProcessNextJob}
                                        disabled={
                                            processNextJobMutation.isPending
                                        }
                                        size="sm"
                                    >
                                        <FaArrowRight />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip content="Reset Stalled Jobs">
                                    <IconButton
                                        aria-label="Reset Stalled Jobs"
                                        onClick={handleResetStalledJobs}
                                        disabled={
                                            resetStalledJobsMutation.isPending
                                        }
                                        size="sm"
                                    >
                                        <FaSync />
                                    </IconButton>
                                </Tooltip>
                            </HStack>
                        </Card.Body>
                    </Card.Root>

                    {/* Queue Single Product Section */}
                    <Card.Root>
                        <Card.Body>
                            <Heading as="h2" size="md" mb={4}>
                                Queue Single Product
                            </Heading>

                            <Flex gap={2} alignItems="center">
                                <Input
                                    placeholder="Enter product ID"
                                    value={productId}
                                    onChange={(e) =>
                                        setProductId(e.target.value)
                                    }
                                    size="md"
                                />
                                <Button
                                    onClick={handleQueueProduct}
                                    colorScheme="blue"
                                    disabled={
                                        queueProductMutation.isPending ||
                                        !productId.trim()
                                    }
                                    size="md"
                                >
                                    Queue
                                </Button>
                            </Flex>

                            <Text fontSize="sm" color="gray.500" mt={2}>
                                Enter the ID of a specific product to add it to
                                the scraping queue.
                            </Text>
                        </Card.Body>
                    </Card.Root>
                </Stack>
            </Container>
        </>
    )
}
