import { createSystem, defaultConfig, defineConfig } from '@chakra-ui/react'

const config = defineConfig({
    theme: {
        tokens: {
            colors: {
                primary: {
                    50: { value: '#fdf2f8' },
                    100: { value: '#fce7f3' },
                    200: { value: '#fbcfe8' },
                    300: { value: '#f9a8d4' },
                    400: { value: '#f472b6' },
                    500: { value: '#ec4899' },
                    600: { value: '#db2777' },
                    700: { value: '#a41752' },
                    800: { value: '#6d0e34' },
                    900: { value: '#45061f' },
                    950: { value: '#2c0514' }
                }
            }
        },
        semanticTokens: {
            colors: {
                primary: {
                    solid: { value: '{colors.primary.500}' },
                    contrast: { value: '{colors.primary.100}' },
                    fg: { value: '{colors.primary.700}' },
                    muted: { value: '{colors.primary.100}' },
                    subtle: { value: '{colors.primary.200}' },
                    emphasized: { value: '{colors.primary.300}' },
                    focusRing: { value: '{colors.primary.500}' }
                }
            }
        }
    },
    globalCss: {
        html: {
            colorPalette: 'primary'
        }
    }
})

export const system = createSystem(defaultConfig, config)
