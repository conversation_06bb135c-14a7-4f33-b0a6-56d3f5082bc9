import {
    Box,
    Card,
    Checkbox,
    Heading,
    Stack,
    Separator
} from '@chakra-ui/react'
import { type Store, type Location, type MaterialType } from '@prisma/client'
import React, { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/router'

// Reusable filter checkbox component
interface FilterCheckboxProps<T extends string> {
    item: T
    isSelected: boolean
    onChange: (item: T) => void
}

function FilterCheckbox<T extends string>({
    item,
    isSelected,
    onChange
}: FilterCheckboxProps<T>) {
    return (
        <Box
            onClick={() => onChange(item)}
            cursor="pointer"
            display="flex"
            alignItems="center"
            py={1}
            px={2}
            width="100%"
            _hover={{ color: 'primary.400' }}
            transition="color 0.2s"
        >
            <Checkbox.Root checked={isSelected} defaultChecked={isSelected}>
                <Checkbox.Control />
            </Checkbox.Root>
            <Box ml={2} flex="1">
                {item}
            </Box>
        </Box>
    )
}

// Filter section component for each category
interface FilterSectionProps<T extends string> {
    title: string
    items: T[]
    selectedItems: T[]
    onChange: (items: T[]) => void
}

function FilterSection<T extends string>({
    title,
    items,
    selectedItems,
    onChange
}: FilterSectionProps<T>) {
    // Toggle item selection
    const handleItemToggle = useCallback(
        (item: T) => {
            onChange(
                selectedItems.includes(item)
                    ? selectedItems.filter((i) => i !== item)
                    : [...selectedItems, item]
            )
        },
        [selectedItems, onChange]
    )

    return (
        <Box>
            <Heading size="sm" mb={2}>
                {title}
            </Heading>
            <Stack direction="column" alignItems="flex-start" gap={1}>
                {items.map((item) => (
                    <FilterCheckbox
                        key={item}
                        item={item}
                        isSelected={selectedItems.includes(item)}
                        onChange={handleItemToggle}
                    />
                ))}
            </Stack>
        </Box>
    )
}

// Location select component
interface LocationSelectProps {
    locations: Location[]
    selectedLocation: Location | null
    onChange: (location: Location) => void
}

function LocationSelect({
    locations,
    selectedLocation,
    onChange
}: LocationSelectProps) {
    return (
        <Box>
            <Heading size="sm" mb={2}>
                Data Source
            </Heading>
            <Box width="100%">
                <select
                    style={{
                        width: '100%',
                        padding: '8px',
                        borderWidth: '1px',
                        borderRadius: '6px',
                        borderColor: 'inherit',
                        backgroundColor: 'inherit',
                        color: 'inherit'
                    }}
                    value={selectedLocation || 'Germany'}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                        const location = e.target.value as Location
                        onChange(location)
                    }}
                >
                    {locations.map((location) => (
                        <option key={location} value={location}>
                            {location}
                        </option>
                    ))}
                </select>
            </Box>
        </Box>
    )
}

interface ProductFiltersProps {
    brands: string[]
    stores: Store[]
    locations: Location[]
    materialTypes: MaterialType[]
    onBrandsChange: (brands: string[]) => void
    onStoresChange: (stores: Store[]) => void
    onLocationsChange: (locations: Location[]) => void
    onMaterialTypesChange: (materialTypes: MaterialType[]) => void
}

export function ProductFilters({
    brands,
    stores,
    locations,
    materialTypes,
    onBrandsChange,
    onStoresChange,
    onLocationsChange,
    onMaterialTypesChange
}: ProductFiltersProps) {
    // Get URL query parameters and router
    const router = useRouter()

    // Track if we're updating from URL to prevent loops
    const [isUpdatingFromUrl, setIsUpdatingFromUrl] = useState(false)

    // State for selected filters - initialize with all items selected
    const [selectedBrands, setSelectedBrands] = useState<string[]>([...brands])
    const [selectedStores, setSelectedStores] = useState<Store[]>([...stores])
    const [selectedMaterialTypes, setSelectedMaterialTypes] = useState<
        MaterialType[]
    >([...materialTypes])
    const [selectedLocation, setSelectedLocation] = useState<Location>(
        'Germany' as Location
    )

    // Track initialization state
    const [initialized, setInitialized] = useState(false)

    // Parse query parameters
    const parseQueryParams = useCallback(() => {
        const {
            brands: brandsParam,
            stores: storesParam,
            location: locationParam,
            materialTypes: materialTypesParam
        } = router.query

        return {
            brands:
                typeof brandsParam === 'string' ? brandsParam.split(',') : [],
            stores:
                typeof storesParam === 'string'
                    ? (storesParam.split(',') as Store[])
                    : [],
            materialTypes:
                typeof materialTypesParam === 'string'
                    ? (materialTypesParam.split(',') as MaterialType[])
                    : [],
            location:
                typeof locationParam === 'string'
                    ? (locationParam as Location)
                    : ('Germany' as Location)
        }
    }, [router.query])

    // Update URL with current filter state
    const updateUrlParams = useCallback(() => {
        if (!initialized || isUpdatingFromUrl) return

        const query: Record<string, string> = {}

        // Update brands param - only add to URL if not all brands are selected
        if (selectedBrands.length < brands.length) {
            if (selectedBrands.length > 0) {
                query.brands = selectedBrands.join(',')
            } else {
                // If no brands selected, add an empty param to indicate this
                query.brands = ''
            }
        }

        // Update stores param - only add to URL if not all stores are selected
        if (selectedStores.length < stores.length) {
            if (selectedStores.length > 0) {
                query.stores = selectedStores.join(',')
            } else {
                // If no stores selected, add an empty param to indicate this
                query.stores = ''
            }
        }

        // Update material types param - only add to URL if not all material types are selected
        if (selectedMaterialTypes.length < materialTypes.length) {
            if (selectedMaterialTypes.length > 0) {
                query.materialTypes = selectedMaterialTypes.join(',')
            } else {
                // If no material types selected, add an empty param to indicate this
                query.materialTypes = ''
            }
        }

        // Update location param
        if (selectedLocation && selectedLocation !== 'Germany') {
            query.location = selectedLocation
        }

        // Update URL without refreshing the page
        router.push(
            {
                pathname: router.pathname,
                query
            },
            undefined,
            { shallow: true }
        )
    }, [
        initialized,
        isUpdatingFromUrl,
        selectedBrands,
        selectedStores,
        selectedMaterialTypes,
        selectedLocation,
        brands.length,
        stores.length,
        materialTypes.length,
        router
    ])

    // Initialize filters from URL or defaults when data is available
    useEffect(() => {
        if (
            !initialized &&
            brands.length > 0 &&
            stores.length > 0 &&
            locations.length > 0 &&
            materialTypes.length > 0
        ) {
            // Check if we have query params
            const params = parseQueryParams()

            // Mark that we're updating from URL to prevent loops
            setIsUpdatingFromUrl(true)

            // Check if we have query params
            const hasBrandsParam = router.query.brands !== undefined
            const hasStoresParam = router.query.stores !== undefined
            const hasMaterialTypesParam =
                router.query.materialTypes !== undefined
            const hasLocationParam = router.query.location !== undefined

            // If we have explicit query params, use them
            if (
                hasBrandsParam ||
                hasStoresParam ||
                hasMaterialTypesParam ||
                hasLocationParam
            ) {
                // Handle brands - if param exists but is empty, select none
                if (hasBrandsParam) {
                    setSelectedBrands(
                        params.brands.length > 0
                            ? params.brands.filter((b) => brands.includes(b))
                            : [] // Empty array if brands param is empty string
                    )
                } else {
                    // No brands param, select all (default)
                    setSelectedBrands([...brands])
                }

                // Handle stores - if param exists but is empty, select none
                if (hasStoresParam) {
                    setSelectedStores(
                        params.stores.length > 0
                            ? params.stores.filter((s) => stores.includes(s))
                            : [] // Empty array if stores param is empty string
                    )
                } else {
                    // No stores param, select all (default)
                    setSelectedStores([...stores])
                }

                // Handle material types - if param exists but is empty, select none
                if (hasMaterialTypesParam) {
                    setSelectedMaterialTypes(
                        params.materialTypes.length > 0
                            ? params.materialTypes.filter((m) =>
                                  materialTypes.includes(m)
                              )
                            : [] // Empty array if material types param is empty string
                    )
                } else {
                    // No material types param, select all (default)
                    setSelectedMaterialTypes([...materialTypes])
                }

                // Handle location
                if (hasLocationParam && locations.includes(params.location)) {
                    setSelectedLocation(params.location)
                } else {
                    // Default to Germany
                    setSelectedLocation('Germany' as Location)
                }
            } else {
                // No query params, select all by default
                setSelectedBrands([...brands])
                setSelectedStores([...stores])
                setSelectedMaterialTypes([...materialTypes])
                setSelectedLocation('Germany' as Location)
            }

            setInitialized(true)

            // Reset the updating flag after a short delay
            setTimeout(() => {
                setIsUpdatingFromUrl(false)
            }, 100)
        }
    }, [
        initialized,
        brands,
        stores,
        locations,
        materialTypes,
        router.query,
        parseQueryParams
    ])

    // Update URL when selections change
    useEffect(() => {
        if (initialized && !isUpdatingFromUrl) {
            // Use a small timeout to debounce rapid changes
            const timeoutId = setTimeout(() => {
                updateUrlParams()
            }, 100)

            return () => clearTimeout(timeoutId)
        }
    }, [initialized, isUpdatingFromUrl, updateUrlParams])

    // Update parent component when selections change
    useEffect(() => {
        if (initialized) {
            onBrandsChange(selectedBrands)
            onStoresChange(selectedStores)
            onMaterialTypesChange(selectedMaterialTypes)
            onLocationsChange(selectedLocation ? [selectedLocation] : [])
        }
    }, [
        initialized,
        selectedBrands,
        selectedStores,
        selectedMaterialTypes,
        selectedLocation,
        onBrandsChange,
        onStoresChange,
        onMaterialTypesChange,
        onLocationsChange
    ])

    return (
        <Box p={4}>
            <Stack direction="row" justifyContent="space-between" mb={4}>
                <Heading size="md">Filters</Heading>
            </Stack>
            <Separator mb={4} />

            <Stack direction="column" gap={4}>
                {/* Location Filter */}
                <Card.Root variant="outline">
                    <Card.Body py={{ base: 3, sm: 2 }} px={{ base: 3, sm: 4 }}>
                        <LocationSelect
                            locations={locations}
                            selectedLocation={selectedLocation}
                            onChange={setSelectedLocation}
                        />
                    </Card.Body>
                </Card.Root>

                {/* Material Types Filter */}
                <Card.Root variant="outline">
                    <Card.Body py={{ base: 3, sm: 2 }} px={{ base: 3, sm: 4 }}>
                        <FilterSection
                            title="Material"
                            items={materialTypes as unknown as string[]}
                            selectedItems={
                                selectedMaterialTypes as unknown as string[]
                            }
                            onChange={(items) =>
                                setSelectedMaterialTypes(
                                    items as unknown as MaterialType[]
                                )
                            }
                        />
                    </Card.Body>
                </Card.Root>

                {/* Brands Filter */}
                <Card.Root variant="outline">
                    <Card.Body py={{ base: 3, sm: 2 }} px={{ base: 3, sm: 4 }}>
                        <FilterSection
                            title="Brands"
                            items={brands}
                            selectedItems={selectedBrands}
                            onChange={setSelectedBrands}
                        />
                    </Card.Body>
                </Card.Root>

                {/* Stores Filter */}
                <Card.Root variant="outline">
                    <Card.Body py={{ base: 3, sm: 2 }} px={{ base: 3, sm: 4 }}>
                        <FilterSection
                            title="Stores"
                            items={stores as unknown as string[]}
                            selectedItems={
                                selectedStores as unknown as string[]
                            }
                            onChange={(items) =>
                                setSelectedStores(items as unknown as Store[])
                            }
                        />
                    </Card.Body>
                </Card.Root>
            </Stack>
        </Box>
    )
}
