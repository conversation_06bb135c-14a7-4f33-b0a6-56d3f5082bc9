import {
    Box,
    Card,
    Heading,
    Stack,
    Text,
    Separator,
    Link,
    Flex
} from '@chakra-ui/react'
import { type Product } from '@prisma/client'
import { FaArrowUpRightFromSquare } from 'react-icons/fa6'

// Extended Product type with productUrl
interface ProductWithUrl extends Product {
    productUrl: string
}

// Utility functions for formatting prices
const formatPrice = (price: number) => {
    return (price / 100).toFixed(2)
}

const formatPricePerKg = (pricePerKg: number) => {
    return (pricePerKg / 100).toFixed(2) // Convert from cents to euros
}

// Product information component (brand, name)
interface ProductInfoProps {
    product: ProductWithUrl
}

function ProductInfo({ product }: ProductInfoProps) {
    return (
        <Text
            fontWeight="medium"
            overflow="hidden"
            textOverflow="ellipsis"
            whiteSpace={{ base: 'normal', xl: 'nowrap' }}
            width={{ base: '100%', xl: 'auto' }}
            _groupHover={{ color: 'primary.400' }}
            _groupActive={{ color: 'primary.400' }}
            _groupFocus={{ color: 'primary.400' }}
        >
            {product.name}
        </Text>
    )
}

// Product details component (material type, weight, color)
interface ProductDetailsProps {
    product: ProductWithUrl
}

function ProductDetails({ product }: ProductDetailsProps) {
    return (
        <Box fontSize="sm" color="gray.600" mr={{ base: 0, xl: 4 }}>
            <Flex flexWrap="wrap" gap={3} alignItems="center">
                {/* Each text item is wrapped in a non-wrapping span */}
                <Text whiteSpace="nowrap">{product.brand}</Text>
                <Text whiteSpace="nowrap">•</Text>
                <Text whiteSpace="nowrap">{product.color}</Text>
                <Text whiteSpace="nowrap">•</Text>
                <Text whiteSpace="nowrap">{product.weight}g</Text>
                <Text whiteSpace="nowrap">•</Text>
                <Text whiteSpace="nowrap">{product.materialType}</Text>
            </Flex>
        </Box>
    )
}

// Product price component
interface ProductPriceProps {
    currentPrice: number
    pricePerKg: number
    currency: string
}

function ProductPrice({
    currentPrice,
    pricePerKg,
    currency
}: ProductPriceProps) {
    return (
        <Stack
            direction={{ base: 'row', xl: 'column' }}
            alignItems={{ base: 'center', xl: 'flex-end' }}
            justifyContent={{ base: 'space-between', xl: 'center' }}
            minWidth={{ base: '100%', xl: '120px' }}
            gap={{ base: 0, xl: 1 }}
            mt={{ base: 2, xl: 0 }}
        >
            <Text fontWeight="bold">
                {formatPrice(currentPrice)} {currency}
            </Text>
            <Text fontSize="sm" color="primary.400">
                {formatPricePerKg(pricePerKg ?? 0)} {currency}/kg
            </Text>
        </Stack>
    )
}

// Product card component
interface ProductCardProps {
    product: ProductWithUrl
}

function ProductCard({ product }: ProductCardProps) {
    const boxHoverStyles = {
        transform: 'scale(1.01)',
        boxShadow: 'md',
        borderColor: 'primary.300',
        outline: 'none',
        boxSizing: 'border-box'
    }

    const iconHoverStyles = {
        color: 'primary.400',
        transform: 'scale(1.1)'
    }

    return (
        <Link
            href={product.productUrl}
            target="_blank"
            rel="noopener noreferrer"
            display="block"
            _focus={{ outline: 'none', boxShadow: 'none' }}
            _active={{ outline: 'none', boxShadow: 'none' }}
            className="group"
        >
            <Card.Root
                variant="outline"
                transition="all 0.2s ease"
                _groupHover={boxHoverStyles}
                _groupActive={boxHoverStyles}
                _groupFocus={boxHoverStyles}
                position="relative"
            >
                <Card.Body py={{ base: 3, xl: 2 }} px={{ base: 3, xl: 4 }}>
                    <Stack
                        direction={{ base: 'column', xl: 'row' }}
                        justifyContent="space-between"
                        alignItems={{ base: 'flex-start', xl: 'center' }}
                        gap={{ base: 6, xl: 8 }} // Increased gap for better separation
                    >
                        <Stack direction="row" alignItems="center" gap={3}>
                            <Box
                                color="gray.400"
                                display="inline-flex"
                                alignItems="center"
                                justifyContent="center"
                                fontSize="sm"
                                bg="transparent"
                                borderRadius="full"
                                p={1.5}
                                transition="all 0.2s ease"
                                _groupHover={iconHoverStyles}
                                _groupActive={iconHoverStyles}
                                _groupFocus={iconHoverStyles}
                            >
                                <FaArrowUpRightFromSquare size="14px" />
                            </Box>
                            <ProductInfo product={product} />
                        </Stack>

                        <Stack
                            direction={{ base: 'column', xl: 'row' }}
                            alignItems={{ base: 'flex-start', xl: 'center' }}
                            gap={{ base: 4, xl: 6 }} // Increased gap for better separation
                            width={{ base: '100%', xl: 'auto' }}
                            ml={{ base: 0, xl: 4 }} // Add left margin on desktop
                        >
                            <ProductDetails product={product} />
                            <ProductPrice
                                currentPrice={product.currentPrice}
                                pricePerKg={product.pricePerKg ?? 0}
                                currency={product.currency}
                            />
                        </Stack>
                    </Stack>
                </Card.Body>
            </Card.Root>
        </Link>
    )
}

// Empty state component
function EmptyState() {
    return (
        <Box textAlign="center" py={10}>
            <Text>No products found matching your filters.</Text>
        </Box>
    )
}

// Main ProductList component
interface ProductListProps {
    products: ProductWithUrl[]
}

export function ProductList({ products }: ProductListProps) {
    return (
        <Box p={4}>
            <Stack direction="row" justifyContent="space-between" mb={4}>
                <Heading size="md">Filaments</Heading>
                <Text>
                    {products.length} {products.length === 1 ? 'roll' : 'rolls'}
                </Text>
            </Stack>
            <Separator mb={4} />

            {products.length > 0 ? (
                <Stack direction="column" gap={2}>
                    {products.map((product) => (
                        <ProductCard key={product.id} product={product} />
                    ))}
                </Stack>
            ) : (
                <EmptyState />
            )}
        </Box>
    )
}
