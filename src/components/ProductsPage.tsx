import { useState, useEffect } from 'react'
import { Grid, GridItem, Box, Skeleton, Stack } from '@chakra-ui/react'
import {
    type Store,
    type Location,
    type Product,
    type MaterialType
} from '@prisma/client'
import { api } from '~/utils/api'
import { ProductFilters } from './ProductFilters'
import { ProductList } from './ProductList'

export function ProductsPage() {
    // Fetch all available filter options - these will use the prefetched data from SSR
    const { data: brands = [] } = api.product.getAllBrands.useQuery()
    const { data: stores = [] } = api.product.getAllStores.useQuery()
    const { data: locations = [] } = api.product.getAllLocations.useQuery()
    const { data: materialTypes = [] } =
        api.product.getAllMaterialTypes.useQuery()

    // State for filters - initialize with empty arrays, will be populated when data is loaded
    const [selectedBrands, setSelectedBrands] = useState<string[]>([])
    const [selectedStores, setSelectedStores] = useState<Store[]>([])
    const [selectedMaterialTypes, setSelectedMaterialTypes] = useState<
        MaterialType[]
    >([])
    const [selectedLocation, setSelectedLocation] = useState<Location>(
        'Germany' as Location
    )

    // Initialize filters only once when component mounts
    const [initialized, setInitialized] = useState(false)

    useEffect(() => {
        // Only initialize once and only when we have data
        if (
            !initialized &&
            brands.length > 0 &&
            stores.length > 0 &&
            locations.length > 0 &&
            materialTypes.length > 0
        ) {
            setSelectedBrands([...brands])
            setSelectedStores([...stores])
            setSelectedMaterialTypes([...materialTypes])
            setSelectedLocation('Germany' as Location)
            setInitialized(true)
        }
    }, [initialized, brands, stores, locations, materialTypes])

    // Debug log to check filter state
    useEffect(() => {
        console.log('ProductsPage - Selected brands:', selectedBrands)
        console.log('ProductsPage - Selected stores:', selectedStores)
        console.log(
            'ProductsPage - Selected material types:',
            selectedMaterialTypes
        )
        console.log('ProductsPage - Selected location:', selectedLocation)
    }, [
        selectedBrands,
        selectedStores,
        selectedMaterialTypes,
        selectedLocation
    ])

    // Fetch filtered products
    const { data: products = [], isLoading } = api.product.getFiltered.useQuery(
        {
            // Always pass the arrays, even if empty
            // This way, if a category is completely deselected, no products will be shown
            brands: selectedBrands,
            stores: selectedStores,
            materialTypes: selectedMaterialTypes,
            location: selectedLocation
        },
        {
            // Only refetch if filters have changed from initial state
            enabled:
                initialized &&
                (selectedBrands.length > 0 ||
                    selectedStores.length > 0 ||
                    selectedMaterialTypes.length > 0 ||
                    selectedLocation !== 'Germany')
        }
    )

    return (
        <Grid templateColumns={{ base: '1fr', md: '300px 1fr' }} gap={6}>
            <GridItem>
                <Box>
                    <ProductFilters
                        brands={brands}
                        stores={stores}
                        locations={locations}
                        materialTypes={materialTypes}
                        onBrandsChange={setSelectedBrands}
                        onStoresChange={setSelectedStores}
                        onMaterialTypesChange={setSelectedMaterialTypes}
                        onLocationsChange={(locations) => {
                            if (locations.length > 0) {
                                setSelectedLocation(locations[0] as Location)
                            } else {
                                setSelectedLocation('Germany' as Location)
                            }
                        }}
                    />
                </Box>
            </GridItem>

            <GridItem>
                {isLoading ? (
                    <Box p={4}>
                        <Skeleton height="40px" mb={4} />
                        <Stack direction="column" gap={2}>
                            {[...Array(8)].map((_, i) => (
                                <Skeleton key={i} height="60px" />
                            ))}
                        </Stack>
                    </Box>
                ) : (
                    <ProductList
                        products={
                            products as (Product & {
                                pricePerKg: number
                                productUrl: string
                            })[]
                        }
                    />
                )}
            </GridItem>
        </Grid>
    )
}
