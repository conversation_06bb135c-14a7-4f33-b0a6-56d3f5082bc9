meta {
  name: AI Scrape and Save
  type: http
  seq: 9
}

post {
  url: {{baseUrl}}/api/trpc/listingScraper.aiScrapeAndSave
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "url": "https://www.3djake.de/filament/pet-filament",
    "config": {
      "endpoint": "http://localhost:11434",
      "model": "llama3.2",
      "temperature": 0.1,
      "maxTokens": 4096,
      "instructions": "Focus on 3D printing filaments. Extract product name, brand, price, material type, weight, and color."
    },
    "options": {
      "headless": true,
      "timeout": 30000
    }
  }
}

docs {
  # AI Scrape and Save
  
  Scrape a single URL using AI to extract product information and save to database.
  
  ## Request Body
  
  - `url`: (string) URL to scrape
  - `config`: AI scraper configuration
    - `endpoint`: (string) Ollama server endpoint URL
    - `model`: (string) Model name to use
    - `temperature`: (number, optional) Temperature for response generation (0-1)
    - `maxTokens`: (number, optional) Maximum tokens for the response
    - `instructions`: (string, optional) Custom instructions for product extraction
  - `options`: (optional) Scraper options
    - `headless`: (boolean, default: true) Whether to run the browser in headless mode
    - `timeout`: (number, default: 30000) Timeout in milliseconds
    - `retries`: (number, default: 3) Number of retries on failure
  
  ## Response
  
  Returns statistics about the scraping operation.
  
  ## Example Response
  
  ```json
  {
    "result": {
      "data": {
        "productsFound": 25,
        "productsSaved": 23,
        "errors": [
          "Failed to save product Example Product: Duplicate identifier"
        ]
      }
    }
  }
  ```
}
