meta {
  name: Test AI Connection
  type: http
  seq: 7
}

post {
  url: {{baseUrl}}/api/trpc/listingScraper.testAiConnection
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "endpoint": "http://localhost:11434",
    "model": "llama3.2",
    "temperature": 0.1,
    "maxTokens": 4096
  }
}

docs {
  # Test AI Connection
  
  Test the connection to an Ollama server instance.
  
  ## Request Body
  
  - `endpoint`: (string) Ollama server endpoint URL
  - `model`: (string) Model name to use
  - `temperature`: (number, optional) Temperature for response generation (0-1)
  - `maxTokens`: (number, optional) Maximum tokens for the response
  - `instructions`: (string, optional) Custom instructions for product extraction
  
  ## Response
  
  Returns a boolean indicating whether the connection was successful.
  
  ## Example Response
  
  ```json
  {
    "result": {
      "data": true
    }
  }
  ```
}
