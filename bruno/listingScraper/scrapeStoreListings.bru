meta {
  name: Scrape Store Listings
  type: http
  seq: 2
}

post {
  url: {{baseUrl}}/api/trpc/listingScraper.scrapeStoreListings
  body: json
  auth: none
}

body:json {
  {
    "json": {
      "store": "prusa3d",
      "options": {
        "headless": true,
        "timeout": 30000,
        "retries": 3,
        "delayMin": 500,
        "delayMax": 3000
      }
    }
  }
}

docs {
  # Scrape Store Listings
  
  Scrapes listings for a specific store.
  
  ## Request Parameters
  
  - `store`: (enum) Store (amazon, prusa3d, threeDJake)
  - `options`: (optional) Scraper options
    - `headless`: (boolean, default: true) Whether to run the browser in headless mode
    - `timeout`: (number, default: 30000) Timeout in milliseconds
    - `retries`: (number, default: 3) Number of retries on failure
    - `delayMin`: (number, default: 500) Minimum delay between requests in milliseconds
    - `delayMax`: (number, default: 3000) Maximum delay between requests in milliseconds
  
  ## Response
  
  Returns a summary of the scraping operation.
}
